/* ========================================
   BAJAJ MOTORS - ORGANIZED CSS STYLES
   ======================================== */

/* ========================================
   1. BASE & UTILITIES
   ======================================== */

/* Text shadow utility */
.text-shadow-lg {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  60% {
    transform: translateY(-5px);
  }
}

/* Bouncing Arrow Animation */
.bouncing-arrow {
  animation: bounce 2s infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.slide-enter {
  animation: slideIn 0.7s ease-out;
}

/* ========================================
   2. HEADER & NAVIGATION
   ======================================== */

/* === TOP BAR SECTION START === */
/* Top Bar Responsive Styles */
.top-bar {
  padding-left: 1rem;
  padding-right: 1rem;
}

/* Responsive padding for top-bar */
@media (min-width: 768px) {
  .top-bar {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1024px) {
  .top-bar {
    padding-left: 4rem;
    padding-right: 4rem;
  }
}

@media (min-width: 1280px) {
  .top-bar {
    padding-left: 6rem;
    padding-right: 6rem;
  }
}

@media (min-width: 1536px) {
  .top-bar {
    padding-left: 150px;
    padding-right: 150px;
  }
}

/* Transparent top bar */
.top-bar-transparent {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Top bar content responsive adjustments */
.top-bar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.top-bar-left {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.top-bar-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Top bar text responsive sizing */
.top-bar-text {
  font-size: 0.875rem;
  font-weight: bold;
  color: #fafafa;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Top bar logo responsive sizing */
.top-bar-logo {
  height: 2rem;
  width: auto;
  flex-shrink: 0;
}

/* Top bar icon responsive sizing */
.top-bar-icon {
  width: 23px;
  height: 18px;
  flex-shrink: 0;
}

/* Main logo responsive sizing */
.main-logo {
  height: 85px;
  width: auto;
  min-width: 120px; /* Set minimum width to prevent horizontal shrinking */
  max-width: 200px; /* Set maximum width for very large screens */
  flex-shrink: 0;
  flex-grow: 0;
  transition: all 0.3s ease;
  object-fit: contain;
  object-position: center;
}

/* Responsive logo sizing - Remove shrinking behavior */
@media (max-width: 768px) {
  .main-logo {
    height: 50px;
    min-width: 70px;
  }
}

@media (max-width: 480px) {
  .main-logo {
    height: 55px; /* Keep same size on very small screens */
    min-width: 120px; /* Maintain minimum width */
  }
}

/* Main logo hover effect */
.main-logo:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}
/* === TOP BAR SECTION END === */

/* === NAVIGATION SECTION START === */
.floating-navbar {
  margin: 0 auto;
  height: 110px;
  padding-left: 0;
  padding-right: 0;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  min-width: 0; /* Prevent container from shrinking */
}

/* Ensure navbar content doesn't shrink */
.floating-navbar > div {
  min-width: 0;
  width: 100%;
}

.floating-navbar .flex {
  min-width: 0;
}

/* Ensure logo container maintains minimum width */
.floating-navbar .flex-1 {
  min-width: 200px; /* Ensure logo area has minimum width */
  flex-shrink: 0;
}

/* Ensure left and right navigation sections don't grow too much */
.floating-navbar .hidden.lg\\:flex {
  flex-shrink: 1;
  min-width: 0;
}

/* Responsive margins for floating navbar */
@media (min-width: 768px) {
  .floating-navbar {
    margin-left: 1rem;
    margin-right: 1rem;
  }
}

@media (min-width: 1024px) {
  .floating-navbar {
    margin-left: 2rem;
    margin-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .floating-navbar {
    margin-left: 4rem;
    margin-right: 4rem;
  }
}

@media (min-width: 1536px) {
  .floating-navbar {
    margin-left: 150px;
    margin-right: 150px;
  }
}

.nav-bg {
  background: linear-gradient(to bottom, #ffffff 0%, #ebebeb 100%);
}

.model-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
}

.tab-btn.active {
  color: #222222;
  border-bottom: 2px solid #222222;
}

.mobile-model-item:hover {
  background-color: #f9fafb;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.media-dropdown {
  min-width: 200px;
}

/* Fixed overlay stacking context */
#mobile-overlay {
  z-index: 40;
  /* Below category detail */
}

#mobile-category-detail {
  z-index: 60;
  /* Above overlay */
}

/* Improved mobile navigation */
.mobile-nav-section {
  transition: all 0.3s ease;
}

.mobile-nav-section.hidden {
  visibility: hidden;
  pointer-events: none;
}

/* Mega Menu Fixed Height */
.mega-menu-container {
  height: 500px;
  /* Fixed height */
  overflow: hidden;
  /* Hide scrollbars for the container */
}

.mega-menu-scrollable {
  height: 100%;
  overflow-y: auto;
  /* Enable scrolling for content */
  scrollbar-width: thin;
  scrollbar-color: #0047ab #f1f1f1;
}

.mega-menu-scrollable::-webkit-scrollbar {
  width: 8px;
}

.mega-menu-scrollable::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.mega-menu-scrollable::-webkit-scrollbar-thumb {
  background: #0047ab;
  border-radius: 4px;
}

.mega-menu-scrollable::-webkit-scrollbar-thumb:hover {
  background: #003380;
}

.categories-sidebar {
  height: 100%;
  overflow-y: auto;
}

.models-section {
  height: calc(100% - 50px);
  /* Account for tabs height */
}

/* Category heading styles */
.category-heading {
  grid-column: 1 / -1;
  text-align: left;
  padding: 10px 0 5px;
  font-weight: 600;
  /* color: #0047ab; */
  margin-top: 15px;
  width: fit-content;
  /* border-bottom: 1px solid #e5e7eb; */
}

.category-heading:first-child {
  margin-top: 0;
}

/* Scrollbar hiding for brand and model tabs */
/* For Webkit browsers (Chrome, Safari, Edge, Opera) */
#brand-tabs-container::-webkit-scrollbar,
#model-tabs-container::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Webkit browsers */
}

/* For Firefox */
#brand-tabs-container,
#model-tabs-container {
  scrollbar-width: none; /* Hide scrollbar for Firefox */
}

/* For Internet Explorer and Edge (legacy) */
#brand-tabs-container,
#model-tabs-container {
  -ms-overflow-style: none; /* Hide scrollbar for IE/Edge */
}

/* Model tabs specific styling */
#model-tabs-container {
  /* Ensure smooth scrolling */
  scroll-behavior: smooth;
  /* Add padding for better touch scrolling on mobile */
  padding-bottom: 2px;
}

/* Model tab items styling */
#model-tabs-container > * {
  flex-shrink: 0; /* Prevent items from shrinking */
}

/* Ensure model tab buttons inside the container are also non-wrapping */
#model-tabs-container .variant-btn {
  flex-shrink: 0 !important;
  white-space: nowrap !important;
  display: inline-block;
}

/* Make sure the model group container also supports horizontal scrolling */
#model-tabs-container .model-group {
  display: inline-block;
  width: 100%;
}

#model-tabs-container .model-group > div {
  display: inline-flex !important;
  flex-wrap: nowrap !important;
  gap: 0.5rem;
}

/* Desktop layout for model tabs - vertical stack on right side */
@media (min-width: 1024px) {
  #model-tabs-container {
    flex-direction: column !important;
    align-items: flex-end !important;
    gap: 0.5rem !important;
  }

  #model-tabs-container .model-group > div {
    flex-direction: column !important;
    align-items: flex-end !important;
    gap: 0.5rem !important;
    border-top: none !important;
    padding: 0 !important;
  }

  #model-tabs-container .variant-btn {
    border-top: none !important;
    border-right: 2px solid transparent !important;
    text-align: right !important;
    padding: 0.5rem 1rem !important;
  }

  #model-tabs-container .variant-btn.active {
    border-right-color: #000 !important;
    border-top: none !important;
  }
}

/* On smaller screens, make model tabs horizontally scrollable */
@media (max-width: 1023px) {
  /* Enable horizontal scrolling only on smaller screens */
  #model-tabs-container {
    overflow-x: auto !important;
    white-space: nowrap !important;
    -webkit-overflow-scrolling: touch;
    flex-direction: row !important;
  }

  #model-tabs-container .model-group > div {
    padding-right: 1rem;
    flex-wrap: nowrap !important;
    flex-direction: row !important;
  }

  #model-tabs-container .variant-btn {
    flex-shrink: 0 !important;
    white-space: nowrap !important;
  }
}

/* Logo container specific styling */
.floating-navbar .flex-1.flex.justify-center {
  min-width: 200px;
  flex-shrink: 0;
  flex-basis: auto;
}

.floating-navbar .flex-1.flex.justify-center a {
  min-width: 120px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* === NAVIGATION SECTION END === */

/* ========================================
   3. HERO SECTION & CAROUSEL
   ======================================== */

/* === SCROLL INDICATOR SECTION START === */
/* Bouncing Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 100px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scroll-indicator:hover {
  transform: translateX(-50%) scale(1.1);
}

.bounce {
  animation: bounce 2s infinite;
}

.scroll-indicator-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.scroll-indicator-icon svg {
  width: 24px;
  height: 24px;
}
/* === SCROLL INDICATOR SECTION END === */

/* === TYPOGRAPHY SECTION START === */
.pulsar-text {
  font-size: 20vw;
}

@media (min-width: 1024px) {
  .pulsar-text {
    font-size: 25vw;
  }
}

.other-brand-text {
  font-size: 15vw;
}

@media (min-width: 1024px) {
  .other-brand-text {
    font-size: 18vw;
  }
}
/* === TYPOGRAPHY SECTION END === */

/* Adjust carousel for header overlap */
.carousel-with-header {
  padding-top: 0;
}

/* Hero Carousel Styles */
.hero-carousel {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.carousel-slide.active {
  opacity: 1;
}

.carousel-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  color: white;
  padding: 1rem;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.carousel-control:hover {
  background: rgba(255, 255, 255, 0.3);
}

.carousel-control.prev {
  left: 1.5rem;
}

.carousel-control.next {
  right: 1.5rem;
}

.carousel-indicators {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
}

.indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.indicator.active {
  background: white;
}

/* ========================================
   4. BIKES SECTION
   ======================================== */

/* === FEATURED CONTENT SECTION START === */
.featured-text-column {
  grid-column: 1 / span 3;
}

.featured-image-column {
  grid-column: 4 / span 7;
}

.featured-text-column,
.featured-image-column {
  transition: transform 0.3s ease;
}

.featured-text-column:hover,
.featured-image-column:hover {
  transform: translateY(-5px);
}

@media (max-width: 1024px) {
  .featured-text-column,
  .featured-image-column {
    grid-column: 1 / -1;
  }
}
/* === FEATURED CONTENT SECTION END === */

/* === EXPERIENCE CAROUSEL SECTION START === */
.expreience-carousel-slide {
  transition: all 0.6s ease;
}

.expreience-slide-image {
  transition: transform 0.3s ease;
}

.expreience-carousel-slide:hover .expreience-slide-image {
  transform: scale(1.05);
}

.expreience-explore-btn {
  transition: all 0.3s ease;
}

.explore-btn:hover {
  transform: translateY(-2px);
}

.expreience-nav-button {
  transition: all 0.3s ease;
}

.expreience-nav-button:hover {
  transform: translateY(-50%) scale(1.1);
}

.expreience-nav-button:active {
  transform: translateY(-50%) scale(0.95);
}

/* Experience section navigation buttons - move closer to carousel */
.expreience-nav-button.prev {
  left: 1.5rem !important;
}
.expreience-nav-button.next {
  right: 1.5rem !important;
}
@media (max-width: 768px) {
  .expreience-nav-button.prev {
    left: 0.75rem !important;
  }
  .expreience-nav-button.next {
    right: 0.75rem !important;
  }
}

/* Ensure the experience section allows overflow for nav buttons */
#experience-carousel {
  overflow: visible !important;
  position: relative;
}

/* Prevent overflow in experience section */
.expreience-carousel-container {
  overflow: hidden;
  position: relative;
}

/* Experience section responsive improvements */
@media (max-width: 640px) {
  .expreience-nav-button {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }

  .expreience-carousel-slide {
    border-radius: 0.75rem;
  }
}

@media (max-width: 480px) {
  .expreience-nav-button {
    width: 1.75rem;
    height: 1.75rem;
    font-size: 0.75rem;
  }

  .expreience-carousel-slide {
    border-radius: 0.5rem;
  }
}
/* === EXPERIENCE CAROUSEL SECTION END === */

/* Motorcycle Card Styles */
.motorcycle-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.motorcycle-card:hover {
  transform: translateY(-2px);
  /* box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); */
}

/* Category Button Styles */
.category-btn {
  border-radius: 0 !important;
  position: relative;
  display: inline-block;
}

.category-btn.active {
  background: transparent;
  color: #222222;
  border-radius: 0 !important;
  text-decoration: underline;
  text-decoration-color: #222222;
  text-decoration-thickness: 2px;
  text-underline-offset: 4px;
}

/* Brand filter active state */
.brand-filter {
  border-radius: 0 !important;
  position: relative;
  display: inline-block;
}

.brand-filter.active {
  background: transparent;
  color: #000000;
  border-radius: 0 !important;
  text-decoration: underline;
  text-decoration-color: #000000;
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
}

/* Color Selection Component Styles */
#color-options {
  min-height: 32px; /* Ensure consistent height even when empty */
}

#color-options button {
  transition: all 0.2s ease;
}

#color-options button:hover {
  transform: scale(1.1);
}

#color-options button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* Bike Carousel Color Buttons */
.color-btn {
  transition: all 0.2s ease;
  cursor: pointer;
}

.color-btn:hover {
  transform: scale(1.1);
  border-color: #374151 !important;
}

.color-btn.active {
  transform: scale(1.15);
  border-color: #000000 !important;
  border-width: 3px !important;
  box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.color-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Brand Logo and Category Section Styles */
#brand-logo {
  transition: all 0.3s ease;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

#category-icon {
  transition: all 0.3s ease;
  opacity: 0.8;
}

#category-display-text {
  transition: all 0.3s ease;
}

/* Logo hover effects */
#brand-logo:hover {
  transform: scale(1.05);
}

/* Series link and variant tabs alignment */
#s1-seriesLink {
  transition: all 0.2s ease;
}

/* Variant tabs alignment */
#variant-tabs {
  transition: all 0.3s ease;
}

#variant-tabs .variant-btn {
  transition: all 0.2s ease;
}

/* Series and variant container alignment */
.flex.justify-end > .flex.flex-col.items-start {
  min-width: fit-content;
}

/* ========================================
   5. DREAM BAJAJ SECTION
   ======================================== */

/* Dream Bajaj Section Styles */
.dream-bajaj-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.dream-bajaj-image {
  transition: transform 0.3s ease;
}

.dream-bajaj-image:hover {
  transform: scale(1.02);
}

/* Loan Availability Button */
.loan-availability-btn {
  background: linear-gradient(135deg, #1f2937, #374151);
  transition: all 0.3s ease;
}

.loan-availability-btn:hover {
  background: linear-gradient(135deg, #374151, #4b5563);
  transform: translateY(-2px);
}

/* ========================================
   5.5. BIKE DETAIL PAGE
   ======================================== */

/* Hero Section for Bike Detail */
.bike-detail-hero {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Color Selector Styles */
.color-selector-button {
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.color-selector-button:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.color-selector-button.active {
  transform: scale(1.15);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Key Features Cards */
.feature-card {
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Specifications Section */
.spec-section {
  transition: all 0.2s ease;
}

.spec-section:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.spec-row {
  transition: background-color 0.2s ease;
}

.spec-row:hover {
  background-color: #f9fafb;
}

/* Specification Tabs */
.spec-tab {
  cursor: pointer;
  position: relative;
}

.spec-tab:hover {
  color: #374151;
}

.spec-tab.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

/* Compact Specification Tabs */
.spec-tab-compact {
  cursor: pointer;
  outline: none;
  transition: all 0.2s ease;
}

.spec-tab-compact:focus {
  outline: none;
}

/* Image Transitions */
#hero-bike-image,
#spec-bike-image {
  transition: opacity 0.3s ease-in-out;
}

/* Smooth image loading */
.image-loading {
  opacity: 0.5;
}

/* Tab Content */
.tab-content {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive adjustments for bike detail */
@media (max-width: 768px) {
  .bike-detail-hero img {
    max-height: 50vh;
  }

  .color-selector-button {
    width: 2rem;
    height: 2rem;
  }

  /* Mobile tab adjustments */
  .spec-tab {
    font-size: 0.875rem;
    padding: 0.75rem 0.5rem;
  }
}

/* ========================================
   6. MOBILE NAVIGATION & MENUS
   ======================================== */

/* Mobile Bikes Dropdown - Full Screen */
#mobile-bikes-dropdown {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: white;
  z-index: 9999;
  transform: translateX(100%);
  transition: transform 0.3s ease;
}

#mobile-bikes-dropdown.show {
  transform: translateX(0);
}

/* Mobile Bikes Arrow Animation */
#mobile-bikes-arrow.rotate {
  transform: rotate(180deg);
}

/* Mobile Menu Styles */
#mobile-menu-overlay {
  z-index: 9999;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

#mobile-menu-overlay.show {
  transform: translateX(0);
}

/* Ensure mobile menu is hidden by default on all screen sizes */
#mobile-menu {
  display: none;
}

#mobile-menu:not(.hidden) {
  display: block;
}

/* Hide mobile menu on desktop screens */
@media (min-width: 1024px) {
  #mobile-menu {
    display: none !important;
  }
}

/* Mobile Menu Item Styles */
.mobile-menu-item {
  transition: all 0.2s ease;
}

.mobile-menu-item:hover {
  background-color: #f9fafb;
}

/* Mobile Brand List Styles */
.mobile-brand-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mobile-brand-item:hover {
  background-color: #f9fafb;
}

.mobile-brand-item:last-child {
  border-bottom: none;
}

/* Mobile Category Tabs */
.mobile-category-tab {
  flex-shrink: 0;
  padding: 0.5rem 1rem;
  margin-right: 0.5rem;
  border-bottom: 2px solid transparent;
  font-weight: 500;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.mobile-category-tab.active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

/* Mobile Motorcycle Item */
.mobile-motorcycle-item {
  display: flex;
  align-items: center;
  padding: 1rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.mobile-motorcycle-item:last-child {
  border-bottom: none;
}

.mobile-motorcycle-item img {
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 0.375rem;
  margin-right: 1rem;
}

/* ========================================
   7. EXPERIENCE SECTION
   ======================================== */

/* Experience Carousel Wrapper */
.experience-carousel-wrapper {
  position: relative;
  overflow: hidden;
  /* Width will be set dynamically by JS to perfectly frame 3 cards */
  padding: 0 1rem;
  /* Add slight padding for visual separation from arrows if needed */
}

.experience-carousel-container {
  display: flex;
  transition: transform 0.5s ease-in-out;
  /* Smooth transition for sliding */
  align-items: center;
  /* Vertically align items to center the different sized cards */
  justify-content: flex-start;
  /* Start items from the left */
  will-change: transform;
}

.experience-carousel-item {
  flex-shrink: 0;
  width: 300px;
  /* Base width for all cards */
  height: 400px;
  /* Base height for all cards (e.g., 300x400 aspect ratio) */
  margin: 0;
  /* No default margin; will be set by JS for overlap */
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transform: scale(0.8);
  /* Smaller scale for side cards */
  transition: transform 0.5s ease-in-out, z-index 0.5s ease-in-out,
    opacity 0.5s ease-in-out, margin 0.5s ease-in-out;
  z-index: 1;
  /* Default z-index for side cards */
  opacity: 0.7;
  /* Dim side cards */
}

/* Focused state for the center card */
.experience-carousel-item.focused {
  transform: scale(1);
  /* Full size for center card */
  z-index: 10;
  /* Bring to front */
  opacity: 1;
  /* Full opacity for focused card */
}

/* Image and button overlay styles */
.card-image-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.card-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.explore-button {
  position: absolute;
  bottom: 1.5rem;
  left: 50%;
  transform: translateX(-50%);
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: #222222;
  color: #ffffff;
  font-weight: bold;
  font-size: 1rem;
  border-radius: 24px;
  text-decoration: none;
  transition: background-color 0.3s, transform 0.3s;
  white-space: nowrap;
  z-index: 10;
  cursor: pointer;
}

.explore-button:hover {
  background-color: #326ad2;
}

/* Navigation arrows */
.nav-arrow {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  border: 1px solid #d1d5db;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.3s;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-arrow:hover {
  background-color: #f3f4f6;
  border-color: #9ca3af;
}

/* ========================================
   8. BLOG SECTION
   ======================================== */

/* Blog bottom row container */
.blog-bottom-row {
  grid-column: 1 / -1;
}

/* Blog media item styles - Note: Some styles are duplicated in Media section */
.media-item {
  transition: background-color 0.2s ease;
}

.media-item:hover {
  background-color: #f3f4f6;
}

/* Rich text content styling for blog posts */
.blog-content {
  line-height: 1.8;
  color: #374151;
}

.blog-content h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 2rem 0 1rem 0;
}

.blog-content h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 1.5rem 0 0.75rem 0;
}

.blog-content p {
  margin: 1rem 0;
}

.blog-content ul,
.blog-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.blog-content li {
  margin: 0.5rem 0;
}

.blog-content strong {
  font-weight: 600;
  color: #111827;
}

.blog-content em {
  font-style: italic;
  color: #6b7280;
}

.blog-content blockquote {
  border-left: 4px solid #3b82f6;
  background-color: #f8fafc;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  font-style: italic;
}

.blog-content blockquote p {
  margin: 0.5rem 0;
}

/* Line clamp utility for blog excerpts */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* ========================================
   9. MOBILE RESPONSIVE STYLES
   ======================================== */

@media (max-width: 768px) {
  /* Top Bar Mobile Adjustments - padding handled in main top-bar section */

  .top-bar-left {
    gap: 0.25rem;
  }

  .top-bar-left img {
    height: 1.5rem;
    width: auto;
  }

  .top-bar-text {
    font-size: 0.75rem;
    display: none; /* Hide text on very small screens */
  }

  .top-bar-right {
    gap: 0.25rem;
  }

  .top-bar-right svg {
    width: 16px;
    height: 16px;
  }

  /* Hero Section Mobile Adjustments */
  #explore-more-btn {
    bottom: 1rem;
    right: 1rem;
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
  }

  #bouncing-arrow {
    bottom: 4.5rem;
  }

  .bouncing-arrow {
    width: 1.5rem;
    height: 1.5rem;
  }

  /* Mobile header adjustments */
  .floating-navbar {
    margin-left: 0;
    margin-right: 0;
    max-width: 100%;
    border-radius: 0.5rem;
  }

  /* Mobile Dream Bajaj Section */
  .dream-bajaj-section h2 {
    font-size: 2rem;
    line-height: 1.2;
  }

  /* Bikes Section Mobile Adjustments */
  #color-options {
    justify-content: flex-end;
  }

  #current-color-name {
    text-align: right;
  }

  /* Stack logo section vertically on mobile */
  .flex.justify-between.items-start {
    flex-direction: column;
    gap: 1rem;
  }

  /* Featured blog layout adjustments for mobile */
  .featured-text-column,
  .featured-image-column {
    grid-column: 1 / -1 !important; /* Full width on mobile */
  }

  /* Hide desktop dropdown on mobile */
  .dropdown-content,
  .media-dropdown-content {
    display: none !important;
  }

  .expreience-explore-btn {
    padding-top: 0.3rem;
    padding-bottom: 0.3rem;
    padding-left: 1rem;
    padding-right: 1rem;
    font-size: 0.85rem;
  }
}

/* Tablet responsive adjustments - padding now handled in main responsive system */
@media (max-width: 1024px) and (min-width: 769px) {
  .top-bar-text {
    font-size: 0.8125rem;
  }
}

/* Small mobile adjustments */
@media (max-width: 480px) {
  .top-bar {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .top-bar-text {
    display: none !important;
  }

  .top-bar-left span,
  .top-bar-right span {
    display: none;
  }

  .top-bar-left {
    justify-content: flex-start;
  }

  .top-bar-right {
    justify-content: flex-end;
  }

  .top-bar-left img {
    height: 1.25rem;
  }

  .top-bar-right svg {
    width: 14px;
    height: 14px;
  }

  .expreience-explore-btn {
    padding-top: 0.2rem;
    padding-bottom: 0.2rem;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
    font-size: 0.75rem;
  }
}

/* Extra small mobile adjustments */
@media (max-width: 360px) {
  .top-bar {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
  }

  .top-bar-content {
    justify-content: space-between;
  }

  .top-bar-left img {
    height: 1rem;
  }

  .top-bar-right svg {
    width: 12px;
    height: 12px;
  }
}

/* Bajaj Experiences Section */
.bajaj-experiences-section {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

/* Experiences Carousel - Use specific class to avoid conflicts */
.expreience-carousel-slide {
  transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
}

.experience-card {
  border-radius: 1.5rem;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  transform-style: preserve-3d;
  backface-visibility: hidden;
}

.experience-card:hover {
  box-shadow: 0 30px 60px rgba(0, 0, 0, 0.25);
}

.experience-card img {
  transition: transform 0.3s ease;
}

.experience-card:hover img {
  transform: scale(1.02);
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Carousel navigation buttons */
#prev-btn,
#next-btn {
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

#prev-btn:hover,
#next-btn:hover {
  background: rgba(255, 255, 255, 0.95) !important;
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 12px 35px rgba(0, 0, 0, 0.2);
}

/* Carousel dots */
.carousel-dot {
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-dot:hover {
  transform: scale(1.2);
}

/* Loading animation */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

/* Explore More Button Styles */
.experience-card button {
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.experience-card:hover button {
  transform: scale(1.05);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Desktop container height for portrait cards */
.bajaj-experiences-section .relative {
  min-height: 500px;
  overflow: hidden;
}

/* Carousel track container */
.bajaj-experiences-section .relative.h-\[500px\] {
  overflow: visible; /* Allow side cards to be visible */
  position: relative;
  width: 100%;
}

/* Ensure carousel container is properly centered */
.bajaj-experiences-section .max-w-6xl {
  position: relative;
  overflow: visible; /* Allow side cards to be visible */
  width: 100%;
  max-width: 1200px; /* Increase max width to accommodate side cards */
}

/* Responsive adjustments for experience carousel */
@media (max-width: 768px) {
  .expreience-carousel-slide {
    width: 224px !important;
    height: 384px !important;
  }

  #prev-btn,
  #next-btn {
    width: 40px;
    height: 40px;
    padding: 8px;
  }

  #prev-btn svg,
  #next-btn svg {
    width: 20px;
    height: 20px;
  }

  .bajaj-experiences-section .relative {
    height: 450px !important;
  }
}

@media (max-width: 640px) {
  .bajaj-experiences-section h2 {
    font-size: 2rem;
    line-height: 1.2;
  }

  .expreience-carousel-slide {
    width: 192px !important;
    height: 320px !important;
  }

  #prev-btn {
    left: 8px;
  }

  #next-btn {
    right: 8px;
  }

  .experience-card button,
  .expreience-explore-btn {
    padding: 0.75rem 1.5rem;
    font-size: 0.75rem;
  }

  .bajaj-experiences-section .relative {
    height: 450px !important;
  }
}

/* Bike Carousel Styles */
.bike-carousel-section {
  padding: 4rem 0;
  background-color: #f9fafb;
}

.brand-selector {
  margin-bottom: 2rem;
}

.brand-btn {
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

.brand-btn.active {
  opacity: 1;
}

.bike-brand-section {
  display: none;
}

.bike-brand-section.active {
  display: block;
}

.bike-card {
  background: white;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.bike-card:hover {
  transform: translateY(-4px);
}

.bike-image {
  padding: 1rem;
  background: #f8f9fa;
}

.bike-info {
  padding: 1rem;
}

.color-options {
  display: flex;
  gap: 0.5rem;
}

.color-btn {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  border: 2px solid transparent;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.color-btn.active {
  border-color: #000;
}

.bike-carousel-controls {
  margin-top: 2rem;
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.bike-carousel-controls button {
  background: #e5e7eb;
  padding: 0.5rem;
  border-radius: 50%;
  transition: background-color 0.3s ease;
}

.bike-carousel-controls button:hover {
  background: #d1d5db;
}

.hide-1150 {
  display: none;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .hero-carousel {
    height: 70vh;
  }

  .carousel-control {
    padding: 0.75rem;
  }

  .bike-card {
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .hero-carousel {
    height: 50vh;
  }

  .carousel-control {
    padding: 0.5rem;
  }

  .brand-selector {
    overflow-x: auto;
    padding-bottom: 1rem;
  }

  .brand-btn {
    flex-shrink: 0;
  }
}

/* Responsive styles for Explore More button in experience section */
@media (max-width: 1024px) {
  .expreience-explore-btn {
    padding: 0.5rem 1.25rem;
    font-size: 0.95rem;
  }
}
@media (max-width: 768px) {
  .expreience-explore-btn {
    padding: 0.4rem 1rem;
    font-size: 0.85rem;
  }
}
@media (max-width: 480px) {
  .expreience-explore-btn {
    padding: 0.3rem 0.75rem;
    font-size: 0.75rem;
  }
}

@media screen and (min-width: 1150px) {
  .hide-1150 {
    display: block; /* or flex/inline depending on your layout */
  }
}
