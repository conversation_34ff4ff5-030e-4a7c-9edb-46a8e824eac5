<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bajaj Motors</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto&display=swap"
      rel="stylesheet"
    />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Roboto+Flex:opsz,wght@8..144,100..1000&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
    <link rel="stylesheet" href="css/styles.css" />
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              primary: "#3b82f6",
              secondary: "#64748b",
              accent: "#326AD2",
              success: "#10b981",
              failure: "#ef4444",
              "bajaj-red": "#E31937",
              "active-text": "#222222",
            },
            fontFamily: {
              roboto: ["Roboto", "sans-serif"],
              robotoFlex: ["Roboto Flex", "sans-serif"],
            },
          },
        },
      };
    </script>
  </head>

  <body class="bg-gray-50 font-roboto">
    <!-- Header -->
    <header class="fixed top-0 left-0 right-0 z-50">
      <!-- Top Bar -->
      <div class="py-2 top-bar top-bar-transparent">
        <div class="top-bar-content">
          <div class="top-bar-left">
            <img
              src="assets/golcha-logo.png"
              alt="golcha_logo"
              class="top-bar-logo"
            />
            <span class="top-bar-text"
              >GOLCHHA GROUP WITH LEGACY OF 100 YEAR</span
            >
          </div>
          <div class="top-bar-right">
            <img src="./assets/globe.png" alt="globe" class="top-bar-icon" />
            <span class="top-bar-text">International website</span>
          </div>
        </div>
      </div>

      <!-- Main Navbar -->
      <nav class="nav-bg floating-navbar bg-white relative">
        <div class="px-6">
          <div class="flex justify-between items-center h-[110px]">
            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="lg:hidden p-2">
              <i class="fas fa-bars text-xl text-gray-700"></i>
            </button>

            <!-- Desktop Navigation - Left Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <!-- Motorcycles Dropdown -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MOTORCYCLES</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>

                <!-- Mega Dropdown - Fixed Height Container -->
                <div
                  class="absolute top-full left-0 w-screen max-w-5xl bg-white shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 mega-menu-container"
                >
                  <div class="flex h-full">
                    <!-- Categories Sidebar - Scrollable -->
                    <div class="w-64 bg-gray-100 p-4 categories-sidebar">
                      <div class="space-y-2">
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent active"
                          data-category="pulsar"
                        >
                          PULSAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="dominar"
                        >
                          DOMINAR
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium over:text-accent"
                          data-category="avengers"
                        >
                          AVENGERS
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="discover"
                        >
                          DISCOVER
                        </button>
                        <button
                          class="category-btn w-full text-center px-3 py-2 text-sm font-medium hover:text-accent"
                          data-category="platina"
                        >
                          PLATINA
                        </button>
                      </div>
                    </div>

                    <!-- Models Section - Scrollable -->
                    <div class="mega-menu-scrollable flex-1 flex flex-col">
                      <div class="p-6">
                        <!-- Category Tabs -->
                        <div
                          id="tabs-container"
                          class="flex space-x-6 mb-4 text-sm boder-b-2"
                        >
                          <!-- Tabs will be dynamically generated -->
                        </div>

                        <!-- Models Content -->
                        <div
                          id="models-content"
                          class="model-grid gap-4 models-section"
                        >
                          <!-- Models will be populated by JavaScript -->
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <a href="/showroom-map.html" class="hover:text-accent flex items-center"
                >SHOWROOMS</a
              >
              <a href="#" class="hover:text-accent flex items-center"
                >WORKSHOPS</a
              >
              <a href="#" class="hover:text-accent flex items-center hide-1150"
                >EVENTS</a
              >
            </div>

            <!-- Centered Logo -->
            <div
              class="flex-1 flex justify-center items-center min-w-0 flex-shrink-0"
            >
              <a
                href="/index.html"
                class="flex items-center flex-shrink-0 min-w-[120px] justify-center"
              >
                <img class="main-logo px-4" src="assets/logo.png" alt="logo" />
              </a>
            </div>

            <!-- Desktop Navigation - Right Side -->
            <div class="hidden lg:flex items-center space-x-8">
              <a
                href="/book-test-ride.html"
                class="hover:text-accent flex items-center"
                >BOOK TEST RIDE</a
              >
              <a
                href="/about.html"
                class="hover:text-accent flex items-center hide-1150"
                >ABOUT US</a
              >
              <a href="#" class="hover:text-accent flex items-center">NEWS</a>

              <!-- Media Center Dropdown - Desktop -->
              <div class="relative group">
                <button class="flex items-center space-x-1 hover:text-accent">
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-down text-xs"></i>
                </button>
                <div
                  class="absolute top-full left-0 w-48 bg-white shadow-lg rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-50 media-dropdown"
                >
                  <div class="py-2">
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ABOUT US</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >ANNOUNCEMENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >EVENTS</a
                    >
                    <a
                      href="/blogs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >BLOGS</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >DOWNLOAD CENTER</a
                    >
                    <a
                      href="#"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >CONTACT US</a
                    >
                    <a
                      href="faqs.html"
                      class="block px-4 py-2 hover:bg-gray-100 hover:text-accent"
                      >FAQS</a
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- Mobile BIKES Button -->
            <div class="lg:hidden relative">
              <button
                id="mobile-bikes-btn"
                class="text-gray-700 font-medium flex items-center space-x-1"
              >
                <span>BIKES</span>
                <i class="fas fa-chevron-down text-xs"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Mobile Menu Overlay - Fixed z-index -->
        <div
          id="mobile-overlay"
          class="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden"
        ></div>

        <!-- Mobile Menu -->
        <div
          id="mobile-menu"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4 bg-white">
            <button
              id="close-mobile-menu"
              class="absolute top-4 right-4 text-xl text-gray-600"
            >
              <i class="fas fa-times"></i>
            </button>

            <!-- Mobile Menu Items -->
            <div class="mt-8 space-y-4">
              <!-- Mobile Motorcycles Dropdown -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>BIKES</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="pulsar"
                  >
                    PULSAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="dominar"
                  >
                    DOMINAR
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="avengers"
                  >
                    AVENGERS
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="discover"
                  >
                    DISCOVER
                  </button>
                  <button
                    class="mobile-category-btn block w-full text-left py-2 text-sm"
                    data-category="platina"
                  >
                    PLATINA
                  </button>
                </div>
              </div>

              <a href="/showroom-map.html" class="block py-2 font-medium">SHOWROOMS</a>
              <a href="#" class="block py-2 font-medium">WORKSHOPS</a>
              <a href="#" class="block py-2 font-medium">EVENTS</a>
              <a href="#" class="block py-2 font-medium">BOOK TEST RIDE</a>
              <a href="#" class="block py-2 font-medium">ABOUT US</a>
              <a href="#" class="block py-2 font-medium">NEWS</a>

              <!-- Media Center Dropdown - Mobile -->
              <div class="mobile-dropdown">
                <button
                  class="mobile-dropdown-btn flex items-center justify-between w-full text-left font-medium text-gray-700 py-2"
                >
                  <span>MEDIA CENTER</span>
                  <i class="fas fa-chevron-right text-xs"></i>
                </button>
                <div class="mobile-dropdown-content hidden pl-4 space-y-2 mt-2">
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ABOUT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >ANNOUNCEMENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >EVENTS</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">BLOGS</a>
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >DOWNLOAD CENTER</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600"
                    >CONTACT US</a
                  >
                  <a href="#" class="block py-2 text-sm text-gray-600">FAQS</a>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mobile Category Detail View - Fixed z-index -->
        <div
          id="mobile-category-detail"
          class="lg:hidden mobile-nav-section fixed top-0 left-0 w-80 h-screen bg-white z-50 transform -translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4">
            <div class="flex items-center mb-4">
              <button id="back-to-categories" class="mr-3 text-gray-600">
                <i class="fas fa-chevron-left"></i>
              </button>
              <button
                id="close-category-detail"
                class="absolute top-4 right-4 text-xl text-gray-600"
              >
                <i class="fas fa-times"></i>
              </button>
              <span id="category-title" class="font-medium text-gray-800"
                >BIKES</span
              >
            </div>

            <!-- Category Tabs -->
            <div
              id="mobile-tabs-container"
              class="flex space-x-4 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Models List -->
            <div
              id="mobile-models-list"
              class="space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>

        <!-- Mobile BIKES Sheet - Same behavior as hamburger menu -->
        <div
          id="mobile-bikes-sheet"
          class="lg:hidden mobile-nav-section fixed top-0 right-0 w-80 h-screen bg-white z-50 transform translate-x-full transition-transform duration-300 hidden"
        >
          <div class="p-4 bg-white relative">
            <div class="flex items-center justify-between mb-4">
              <span
                id="mobile-bikes-header"
                class="font-medium text-gray-800 uppercase"
                >Bikes</span
              >
              <button id="close-bikes-sheet" class="text-xl text-gray-600">
                <i class="fas fa-times"></i>
              </button>
            </div>

            <button
              id="back-to-bikes-categories"
              class="hidden items-center text-sm text-gray-600 mb-4"
            >
              <i class="fas fa-chevron-left mr-2"></i>
              Back
            </button>

            <!-- Categories List -->
            <div id="mobile-bikes-categories-list" class="space-y-1 mt-4">
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="pulsar"
              >
                <span>PULSAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="dominar"
              >
                <span>DOMINAR</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="avengers"
              >
                <span>AVENGERS</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="discover"
              >
                <span>DISCOVER</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
              <button
                class="mobile-bikes-category-btn w-full flex justify-between items-center py-3 text-left font-medium"
                data-category="platina"
              >
                <span>PLATINA</span>
                <i class="fas fa-chevron-right text-xs"></i>
              </button>
            </div>

            <!-- Category Tabs (hidden initially) -->
            <div
              id="mobile-bikes-tabs-container"
              class="hidden flex gap-x-4 gap-y-2 mb-4 text-sm border-b pb-2"
            >
              <!-- Tabs will be dynamically generated -->
            </div>

            <!-- Mobile Bikes Models List (hidden initially) -->
            <div
              id="mobile-bikes-models-content"
              class="hidden space-y-3 max-h-[calc(100vh-200px)] overflow-y-auto scrollbar-hide"
            >
              <!-- Models will be populated by JavaScript -->
            </div>
          </div>
        </div>
      </nav>
    </header>

    <!-- Hero Carousel Section -->
    <section class="hero-carousel relative">
      <div class="carousel-container">
        <!-- Slide 1 -->
        <div class="carousel-slide active" id="hero-slide-1">
          <img
            src="./assets/hero_image_1.png"
            alt="Hero Image 1"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- Slide 2 -->
        <div class="carousel-slide" id="hero-slide-2">
          <img
            src="https://images.unsplash.com/photo-1659211573091-20ae39f8f159?q=80&w=871&auto=format&fit=crop"
            alt="Hero Image 2"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- Slide 3 -->
        <div class="carousel-slide" id="hero-slide-3">
          <img
            src="https://images.unsplash.com/photo-1697683051660-01921a39bb62?q=80&w=870&auto=format&fit=crop"
            alt="Hero Image 3"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- Slide 4 -->
        <div class="carousel-slide" id="hero-slide-4">
          <img
            src="https://images.pexels.com/photos/18699418/pexels-photo-18699418.jpeg?_gl=1*dg5akz*_ga*ODY2NzQ5NTM5LjE3NTA5NTMyNTY.*_ga_8JE65Q40S6*czE3NTA5NTMyNTYkbzEkZzEkdDE3NTA5NTMzNTQkajUxJGwwJGgw"
            alt="Hero Image 4"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- Slide 5 -->
        <div class="carousel-slide" id="hero-slide-5">
          <img
            src="https://images.unsplash.com/photo-1571068316344-75bc76f77890?w=1920&h=1080&fit=crop"
            alt="Hero Image 5"
            class="w-full h-full object-cover"
          />
        </div>

        <!-- Carousel Controls -->
        <button class="carousel-control prev" aria-label="Previous slide">
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            ></path>
          </svg>
        </button>
        <button class="carousel-control next" aria-label="Next slide">
          <svg
            class="w-6 h-6"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            ></path>
          </svg>
        </button>

        <!-- Carousel Indicators -->
        <div class="carousel-indicators">
          <button
            class="indicator active"
            data-slide="0"
            aria-label="Go to slide 1"
          ></button>
          <button
            class="indicator"
            data-slide="1"
            aria-label="Go to slide 2"
          ></button>
          <button
            class="indicator"
            data-slide="2"
            aria-label="Go to slide 3"
          ></button>
          <button
            class="indicator"
            data-slide="3"
            aria-label="Go to slide 4"
          ></button>
          <button
            class="indicator"
            data-slide="4"
            aria-label="Go to slide 5"
          ></button>
        </div>

        <!-- Bouncing Scroll Indicator -->
        <div class="scroll-indicator bounce" id="scroll-to-bikes">
          <div class="scroll-indicator-icon">
            <img src="./assets/down-btn.png" alt="" />
          </div>
        </div>
      </div>
    </section>

    <!-- Bike Carousel Section -->
    <section
      id="bike-carousel"
      class="w-full bg-white relative overflow-hidden"
    >
      <div
        class="absolute inset-0 flex items-center justify-center pointer-events-none"
      >
        <h1
          class="brand-text pulsar-text font-robotoFlex xt-[15vw] lg:text-[20vw] font-black text-indigo-100 select-none"
          id="background-brand-text"
        >
          PULSAR
        </h1>
      </div>

      <div class="relative z-10 pt-8 px-12">
        <nav class="flex justify-center">
          <div
            class="inline-flex space-x-8 lg:space-x-16 border-b border-gray-200 pl-8 pr-8 overflow-x-auto whitespace-nowrap lg:overflow-x-visible lg:whitespace-normal"
            id="brand-tabs-container"
          ></div>
        </nav>
      </div>

      <div class="relative z-10 max-w-7xl mx-auto px-4 py-8">
        <div class="text-center mb-8">
          <h2
            class="text-3xl lg:text-5xl font-bold text-gray-900 mb-4"
            id="bike-title"
          >
            PULSAR 220F ABS
          </h2>
          <p
            class="text-gray-600 text-base lg:text-lg max-w-3xl mx-auto leading-relaxed"
            id="bike-description"
          >
            Experience the perfect blend of power and style with the Pulsar 220F
            ABS.
          </p>
        </div>

        <div
          class="grid grid-cols-12 gap-4 lg:gap-8 items-center min-h-[500px]"
        >
          <div class="col-span-2 flex justify-center items-center">
            <button
              class="bike-prev-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200"
              id="bike-prev-btn"
            >
              <svg
                class="w-6 h-6 lg:w-7 lg:h-7"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M15 19l-7-7 7-7"
                ></path>
              </svg>
            </button>
          </div>

          <div class="col-span-8 flex justify-center items-center">
            <div class="relative w-full max-w-4xl">
              <img
                src="./assets/bikes/pulsar/pulsar_220f_abs.png"
                alt="Pulsar 220F ABS"
                class="w-full h-auto max-h-[400px] lg:max-h-[500px] object-contain"
                id="main-bike-image"
              />
            </div>
          </div>

          <div class="col-span-2 flex justify-center items-center">
            <button
              class="bike-next-btn w-12 h-12 lg:w-14 lg:h-14 rounded-full border border-gray-300 flex items-center justify-center hover:border-gray-500 hover:bg-gray-50 transition-all duration-200"
              id="bike-next-btn"
            >
              <svg
                class="w-6 h-6 lg:w-7 lg:h-7"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </button>
          </div>
        </div>

        <div class="mt-8">
          <div
            class="flex items-center justify-between mb-6 px-4 gap-4 flex-wrap md:flex-nowrap"
          >
            <div class="flex items-center space-x-4 min-w-0">
              <img
                src="./assets/brand-logos/pulsar-logo.png"
                alt="Pulsar Logo"
                class="h-8"
                id="brand-logo"
              />
              <div class="flex items-center space-x-2">
                <img
                  src="./assets/icons/sports.png"
                  alt="Sports Category"
                  class="h-6"
                  id="category-icon"
                />
                <span class="text-gray-600" id="category-text">NS</span>
              </div>
            </div>

            <div
              class="flex items-center space-x-4 shrink-0"
              id="color-selection"
            ></div>
          </div>

          <div class="w-full px-4 mb-4">
            <a
              href="bike-detail.html"
              class="inline-flex items-center justify-end hover:text-blue-800 transition-colors duration-200"
            >
              View Series page
              <svg
                class="w-4 h-4 ml-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 5l7 7-7 7"
                ></path>
              </svg>
            </a>
          </div>
          <div
            class="flex justify-end gap-4 pt-4 overflow-x-auto whitespace-nowrap lg:overflow-x-visible lg:whitespace-normal lg:flex-wrap lg:justify-end"
            id="model-tabs-container"
          ></div>
        </div>
      </div>
    </section>

    <!-- Own Your Dream Bajaj Section -->
    <section class="py-16 dream-bajaj-section">
      <div class="max-w-7xl mx-auto px-6">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <!-- Image Section -->
          <div class="relative">
            <div
              class="rounded-2xl overflow-hidden shadow-2xl dream-bajaj-image"
            >
              <img
                src="/assets/dream-section-img.png"
                alt="Bajaj Motorcycle with Customer"
                class="w-full h-[400px] object-cover"
              />
            </div>
          </div>

          <!-- Content Section -->
          <div class="space-y-6">
            <h2
              class="text-4xl font-bold text-gray-900 leading-tight text-failure"
            >
              OWN YOUR DREAM BAJAJ <br />
              MOTORCYCLE WITH EASE
            </h2>

            <p class="text-lg text-gray-600 leading-relaxed">
              At Bajaj Nepal, we believe that your dream ride should be within
              reach. Our simplified financing options are designed to make
              motorcycle ownership accessible and hassle-free for everyone.
            </p>

            <div class="pt-4">
              <button
                class="loan-availability-btn bg-[#222222] h-10 flex items-center text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Check Your Loan Availability
                <svg
                  class="w-6 h-6 group-hover:scale-110 transition-transform"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  ></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Bajaj Expereience Section -->
    <section
      class="bg-[#F5F5F5] min-h-screen flex items-center justify-center p-4 lg:p-8 overflow-hidden"
    >
      <div class="w-full max-w-6xl mx-auto">
        <h1
          class="text-center text-2xl md:text-3xl lg:text-4xl font-bold text-slate-800 mb-8 lg:mb-12 tracking-widest uppercase"
        >
          The Bajaj Experiences
        </h1>

        <div
          class="relative h-[400px] sm:h-[450px] md:h-[500px] lg:h-[600px] flex items-center justify-center overflow-hidden px-4 sm:px-8 md:px-12"
        >
          <div
            id="carousel"
            class="relative w-full h-full flex items-center justify-center overflow-hidden max-w-sm sm:max-w-md md:max-w-lg lg:max-w-2xl xl:max-w-4xl"
          >
            <div
              class="expreience-carousel-slide absolute w-48 h-80 sm:w-56 sm:h-96 md:w-72 md:h-[400px] lg:w-80 lg:h-[420px] xl:w-[420px] xl:h-[550px] 2xl:w-[480px] 2xl:h-[630px] rounded-xl md:rounded-2xl lg:rounded-3xl overflow-hidden shadow-2xl"
              data-index="0"
            >
              <div class="relative w-full h-full overflow-hidden rounded-3xl">
                <img
                  src="/assets//experience/1.png"
                  alt="Red Sports Motorcycle"
                  class="expreience-slide-image w-full h-full object-cover"
                />
                <div
                  class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 flex justify-center items-end hidden"
                >
                  <button
                    class="expreience-explore-btn bg-black hover:bg-accent text-white px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl"
                  >
                    Explore More
                  </button>
                </div>
              </div>
            </div>

            <div
              class="expreience-carousel-slide absolute w-48 h-80 sm:w-56 sm:h-96 md:w-72 md:h-[400px] lg:w-80 lg:h-[420px] xl:w-[420px] xl:h-[550px] 2xl:w-[480px] 2xl:h-[630px] rounded-xl md:rounded-2xl lg:rounded-3xl overflow-hidden shadow-2xl bg-white"
              data-index="1"
            >
              <div class="relative w-full h-full overflow-hidden rounded-3xl">
                <img
                  src="/assets//experience/2.png"
                  alt="Blue Commuter Bike"
                  class="expreience-slide-image w-full h-full object-cover"
                />
                <div
                  class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 flex justify-center items-end hidden"
                >
                  <button
                    class="expreience-explore-btn bg-black hover:bg-accent text-white px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl"
                  >
                    Explore More
                  </button>
                </div>
              </div>
            </div>

            <div
              class="expreience-carousel-slide absolute w-48 h-80 sm:w-56 sm:h-96 md:w-72 md:h-[400px] lg:w-80 lg:h-[420px] xl:w-[420px] xl:h-[550px] 2xl:w-[480px] 2xl:h-[630px] rounded-xl md:rounded-2xl lg:rounded-3xl overflow-hidden shadow-2xl bg-white"
              data-index="2"
            >
              <div class="relative w-full h-full overflow-hidden rounded-3xl">
                <img
                  src="/assets//experience/3.png"
                  alt="Cruiser Motorcycle"
                  class="expreience-slide-image w-full h-full object-cover"
                />
                <div
                  class="explore-btn-wrapper absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-6 flex justify-center items-end hidden"
                >
                  <button
                    class="expreience-explore-btn bg-black hover:bg-accent text-white px-6 py-3 rounded-full text-sm font-bold uppercase tracking-wide shadow-lg hover:shadow-xl"
                  >
                    Explore More
                  </button>
                </div>
              </div>
            </div>
          </div>

          <button
            id="prevBtn"
            class="expreience-nav-button prev absolute top-1/2 -translate-y-1/2 left-2 sm:left-4 md:-left-8 lg:-left-12 bg-white/90 hover:bg-white w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full shadow-lg text-sm sm:text-lg md:text-xl font-bold text-slate-700 z-10"
          >
            ‹
          </button>

          <button
            id="nextBtn"
            class="expreience-nav-button next absolute top-1/2 -translate-y-1/2 right-2 sm:right-4 md:-right-8 lg:-right-12 bg-white/90 hover:bg-white w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 rounded-full shadow-lg text-sm sm:text-lg md:text-xl font-bold text-slate-700 z-10"
          >
            ›
          </button>
        </div>
      </div>
    </section>

    <!-- Blog Section -->
    <section class="py-16 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Blog Grid - Static Content -->
        <div class="grid grid-cols-1 lg:grid-cols-10 gap-6">
          <!-- Featured Blog Text -->
          <div class="group cursor-pointer featured-text-column">
            <article class="overflow-hidden transition-all duration-300 h-full">
              <div class="">
                <div class="mb-4">
                  <span class="text-sm text-gray-500 font-medium"
                    >May 12, 2025</span
                  >
                </div>
                <h3
                  class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 group-hover:text-blue-600 transition-colors"
                >
                  WHEELS OF MEMORY
                </h3>
                <p class="text-gray-600 mb-6 leading-relaxed">
                  Join us for the 12 months photography contest inviting riders
                  to share bike photos symbolizing each of the 12 Indian months.
                  Capture the essence of time and memories on two wheels.
                </p>
                <button
                  class="inline-flex items-center bg-gray-900 text-white px-6 py-2 rounded-full font-medium hover:bg-gray-800 transition-colors group"
                >
                  <span>LEARN MORE</span>
                  <svg
                    class="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 5l7 7-7 7"
                    ></path>
                  </svg>
                </button>
              </div>
            </article>
          </div>

          <!-- Featured Blog Image -->
          <div class="group cursor-pointer featured-image-column">
            <div
              class="relative h-80 lg:h-96 overflow-hidden rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
            >
              <img
                src="/assets/blogs/1.jpg"
                alt="Wheels of Memory Photography Contest"
                class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                loading="lazy"
              />
              <div
                class="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"
              ></div>
            </div>
          </div>

          <!-- Bottom Row Container -->
          <div class="lg:col-span-10 blog-bottom-row">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
              <!-- Blog Card 1 -->
              <article class="blog-card cursor-pointer overflow-hidden">
                <div class="relative h-52 overflow-hidden">
                  <img
                    src="/assets/blogs/2.jpg"
                    alt="Best Motorcycle Routes in Nepal"
                    class="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  />
                </div>
                <div class="mt-4">
                  <h4 class="text-gray-600 text-base">
                    Best Motorcycle Routes in Nepal
                  </h4>
                </div>
              </article>

              <!-- Blog Card 2 -->
              <article class="blog-card cursor-pointer overflow-hidden">
                <div class="relative h-52 overflow-hidden">
                  <img
                    src="/assets/blogs/3.jpg"
                    alt="Best Motorcycle Routes in Nepal"
                    class="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  />
                </div>
                <div class="mt-4">
                  <h4 class="text-gray-600 text-base">
                    Best Motorcycle Routes in Nepal
                  </h4>
                </div>
              </article>

              <!-- Blog Card 3 -->
              <article class="blog-card cursor-pointer overflow-hidden">
                <div class="relative h-52 overflow-hidden">
                  <img
                    src="/assets/blogs/4.png"
                    alt="Best Motorcycle Routes in Nepal"
                    class="w-full h-full object-cover rounded-lg hover:scale-105 transition-transform duration-500"
                    loading="lazy"
                  />
                </div>
                <div class="mt-4">
                  <h4 class="text-gray-600 text-base">
                    Best Motorcycle Routes in Nepal
                  </h4>
                </div>
              </article>
            </div>
          </div>
        </div>

        <!-- View All Blogs Button -->
        <div class="text-center mt-12">
          <a
            href="/blogs.html"
            class="bg-gray-900 text-white px-8 py-3 rounded-full font-semibold hover:bg-accent transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            View All Stories
          </a>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-50 min-h-screen flex flex-col">
      <!-- Email Signup Section -->
      <div class="flex-1 flex items-center justify-center px-4 py-12">
        <div class="max-w-md w-full">
          <div class="text-center mb-8">
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              Sign up for Email
            </h2>
            <p class="text-sm text-gray-500 mb-1">
              Read our
              <a href="#" class="text-blue-500 underline">privacy policy</a>
              to learn about data processing
            </p>
            <p class="text-sm text-gray-500">
              Sign up for BAJAJ latest news and updates
            </p>
          </div>

          <form id="emailForm" class="mb-4">
            <div class="flex gap-2 mb-2">
              <input
                type="email"
                id="email"
                placeholder="YOUR EMAIL ADDRESS"
                class="flex-1 bg-white border border-gray-300 rounded-md px-4 py-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                required
              />
              <button
                type="submit"
                class="bg-blue-500 text-white px-6 py-3 rounded-md text-sm font-medium hover:bg-blue-600"
              >
                SUBSCRIBE NOW
              </button>
            </div>
            <p class="text-xs text-gray-500 text-center">
              This site is protected by reCAPTCHA and the Google
              <a href="#" class="underline">Privacy Policy</a> and
              <a href="#" class="underline">Terms of Service</a> apply.
            </p>
          </form>
        </div>
      </div>

      <!-- Footer Section -->
      <div class="bg-[#0F0F0F] text-white py-12">
        <div class="max-w-6xl mx-auto px-4">
          <div class="text-center mb-8">
            <div class="flex justify-center items-center mb-4">
              <div class="w-12 h-12 mr-3 flex items-center justify-center">
                <img src="/assets/golcha-logo.png" alt="" />
              </div>
              <h3 class="text-xl font-semibold">
                GOLCHHA GROUP WITH LEGACY OF 100 YEAR
              </h3>
            </div>
          </div>

          <!-- Footer Links -->
          <div class="flex justify-center gap-8 text-sm mb-8">
            <a href="#" class="hover:text-gray-300">TERMS OF USE</a>
            <a href="#" class="hover:text-gray-300">PRIVACY INFORMATION</a>
            <a href="#" class="hover:text-gray-300">COOKIES INFORMATION</a>
          </div>

          <!-- Copyright -->
          <div class="text-center text-xs text-gray-400 mb-8">
            <p>
              Copyright © 2025 Bajaj Auto Ltd – A Sole Shareholder Company - A
              Company subject to the Management and Coordination
            </p>
            <p>activities of BAJAJ AUTO. All rights reserved. VAT NO.</p>
          </div>

          <!-- Bottom Section -->
          <div class="flex flex-wrap justify-between items-center gap-4">
            <!-- Bajaj Logo -->
            <div class="h-16 w-32">
              <img src="/assets/logo.png" alt="" />
            </div>

            <!-- Social Media Icons -->
            <div class="flex gap-4 text-2xl">
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-instagram"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-facebook"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-youtube"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-tiktok"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-twitter"></i>
              </a>
              <a
                href="#"
                class="text-gray-400 hover:text-white transition-transform transform hover:-translate-y-1"
              >
                <i class="fab fa-linkedin"></i>
              </a>
            </div>

            <!-- International Website -->
            <div
              class="flex items-center gap-4 text-lg font-semibold text-white"
            >
              <img class="w-8 h-8" src="/assets/globe.png" alt="" />
              <a href="#" class="hover:text-white">International website</a>
            </div>
          </div>
        </div>
      </div>
    </footer>

    <script>
      document
        .getElementById("emailForm")
        .addEventListener("submit", function (e) {
          e.preventDefault();
          const email = document.getElementById("email").value;
          if (email) {
            alert(
              "Thank you for subscribing! You will receive updates at: " + email
            );
            document.getElementById("email").value = "";
          }
        });
    </script>

    <!-- Hero Carousel Functionality Script -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        console.log("Backup carousel script running...");

        // Check if elements exist
        const slides = document.querySelectorAll(".carousel-slide");
        const indicators = document.querySelectorAll(
          ".carousel-indicators .indicator"
        );
        const prevBtn = document.querySelector(".carousel-control.prev");
        const nextBtn = document.querySelector(".carousel-control.next");

        console.log("Backup - Slides:", slides.length);
        console.log("Backup - Indicators:", indicators.length);
        console.log("Backup - Prev button:", prevBtn);
        console.log("Backup - Next button:", nextBtn);

        if (slides.length > 0 && prevBtn && nextBtn) {
          let currentSlide = 0;
          let autoplayInterval;

          function showSlide(index) {
            slides.forEach((slide) => slide.classList.remove("active"));
            indicators.forEach((indicator) =>
              indicator.classList.remove("active")
            );

            if (slides[index]) {
              slides[index].classList.add("active");
            }
            if (indicators[index]) {
              indicators[index].classList.add("active");
            }
            currentSlide = index;
          }

          function nextSlide() {
            const next = (currentSlide + 1) % slides.length;
            showSlide(next);
          }

          function prevSlide() {
            const prev = (currentSlide - 1 + slides.length) % slides.length;
            showSlide(prev);
          }

          function startAutoplay() {
            stopAutoplay();
            autoplayInterval = setInterval(nextSlide, 4000);
          }

          function stopAutoplay() {
            if (autoplayInterval) {
              clearInterval(autoplayInterval);
            }
          }

          // Add event listeners
          prevBtn.addEventListener("click", function (e) {
            e.preventDefault();
            stopAutoplay();
            prevSlide();
            startAutoplay();
          });

          nextBtn.addEventListener("click", function (e) {
            e.preventDefault();
            stopAutoplay();
            nextSlide();
            startAutoplay();
          });

          indicators.forEach((indicator, index) => {
            indicator.addEventListener("click", function (e) {
              e.preventDefault();
              stopAutoplay();
              showSlide(index);
              startAutoplay();
            });
          });

          // Hover pause
          const carousel = document.querySelector(".hero-carousel");
          if (carousel) {
            carousel.addEventListener("mouseenter", stopAutoplay);
            carousel.addEventListener("mouseleave", startAutoplay);
          }

          // Start autoplay
          startAutoplay();
          console.log("Backup carousel initialized successfully");
        }
      });
    </script>

    <!-- Navbar Functionality Scripts -->
    <script>
      // Comprehensive motorcycle data with brand-specific categories
      const motorcycleData = {
        pulsar: {
          classic: [
            {
              name: "PULSAR 220F ABS",
              image: "./assets/bikes/pulsar/pulsar_220f_abs.png",
            },
            {
              name: "PULSAR 150 TD",
              image: "/assets/bikes/pulsar/pulsar_150_td.png",
            },
            {
              name: "PULSAR 150",
              image: "/assets/bikes/pulsar/pulsar_150.png",
            },
            {
              name: "PULSAR 125",
              image: "/assets/bikes/pulsar/pulsar_125.png",
            },
          ],
          ns: [
            {
              name: "PULSAR NS400Z",
              image: "/assets/bikes/pulsar/ns_400z.png",
            },
            {
              name: "PULSAR NS 200 ABS FI",
              image: "/assets/bikes/pulsar/ns_200_abs_fi.png",
            },
            {
              name: "PULSAR NS 200 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 200",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS200",
            },
            {
              name: "PULSAR NS 160 ABS",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160",
            },
            {
              name: "PULSAR NS160 FI DUAL ABS BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS160BS6",
            },
            {
              name: "PULSAR NS 125 BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125BS6",
            },
            {
              name: "PULSAR NS 125 FI BS6",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=NS125FIB",
            },
          ],
          n: [
            {
              name: "PULSAR N250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N250",
            },
            {
              name: "PULSAR N160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=N160",
            },
          ],
        },
        dominar: {
          classic: [
            {
              name: "DOMINAR 400",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D400",
            },
            {
              name: "DOMINAR 250",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D250",
            },
          ],
        },
        avengers: {
          cruiser: [
            {
              name: "AVENGER CRUISE 220",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV220",
            },
            {
              name: "AVENGER STREET 160",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=AV160",
            },
          ],
        },
        discover: {
          commuter: [
            {
              name: "DISCOVER 125",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D125",
            },
            {
              name: "DISCOVER 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=D110",
            },
          ],
        },
        platina: {
          commuter: [
            {
              name: "PLATINA 110",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL110",
            },
            {
              name: "PLATINA 100",
              image:
                "https://via.placeholder.com/120x80/0047AB/FFFFFF?text=PL100",
            },
          ],
        },
      };

      let currentCategory = "pulsar";
      let currentTab = "all";

      // DOM elements
      const mobileOverlay = document.getElementById("mobile-overlay");
      const mobileMenu = document.getElementById("mobile-menu");
      const mobileCategoryDetail = document.getElementById(
        "mobile-category-detail"
      );
      const mobileBikesSheet = document.getElementById("mobile-bikes-sheet");
      const mobileMenuBtn = document.getElementById("mobile-menu-btn");
      const mobileBikesBtn = document.getElementById("mobile-bikes-btn");
      const closeMobileMenu = document.getElementById("close-mobile-menu");
      const closeBikesSheet = document.getElementById("close-bikes-sheet");
      const backToCategories = document.getElementById("back-to-categories");
      const closeCategoryDetail = document.getElementById(
        "close-category-detail"
      );

      // Mobile Bikes Sheet Elements
      const mobileBikesHeader = document.getElementById("mobile-bikes-header");
      const backToBikesCategories = document.getElementById(
        "back-to-bikes-categories"
      );
      const mobileBikesCategoriesList = document.getElementById(
        "mobile-bikes-categories-list"
      );
      const mobileBikesTabsContainer = document.getElementById(
        "mobile-bikes-tabs-container"
      );
      const mobileBikesModelsContent = document.getElementById(
        "mobile-bikes-models-content"
      );

      // Function to format sub-category names
      function formatSubCategoryName(name) {
        if (name === "ns") return "NS";
        if (name === "n") return "N";
        if (name === "classic") return "Classic";
        if (name === "cruiser") return "Cruiser";
        if (name === "commuter") return "Commuter";
        return name.charAt(0).toUpperCase() + name.slice(1);
      }

      // Function to render tabs dynamically
      function renderTabs(category, isMobile = false, customContainer = null) {
        const tabsContainer = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-tabs-container")
          : document.getElementById("tabs-container");

        tabsContainer.innerHTML = "";

        // Get available tabs for this category
        const availableTabs = Object.keys(motorcycleData[category]).filter(
          (tab) => motorcycleData[category][tab].length > 0
        );

        // Always show "All" tab
        const allTab = document.createElement("button");
        allTab.className = `tab-btn ${
          currentTab === "all"
            ? "border-b-2 border-active-text font-medium active"
            : "text-active-text font-medium"
        } pb-1`;
        allTab.dataset.tab = "all";
        allTab.textContent = "All";
        tabsContainer.appendChild(allTab);

        // Create tabs for each available category
        availableTabs.forEach((tabName) => {
          const tab = document.createElement("button");
          tab.className = `tab-btn ${
            currentTab === tabName
              ? "border-b-2 border-active-text active font-medium"
              : "text-active-text font-medium"
          } pb-1`;
          tab.dataset.tab = tabName;
          tab.textContent = formatSubCategoryName(tabName);
          tabsContainer.appendChild(tab);
        });

        // Add event listeners to new tabs
        addTabEventListeners(isMobile);
      }

      // Function to render models
      function renderModels(
        category,
        tab,
        isMobile = false,
        customContainer = null
      ) {
        const container = customContainer
          ? document.getElementById(customContainer)
          : isMobile
          ? document.getElementById("mobile-models-list")
          : document.getElementById("models-content");

        let models = [];

        if (tab === "all") {
          // Create container HTML with category headings
          let containerHTML = "";

          // Get all sub-categories for this category
          const subCategories = Object.keys(motorcycleData[category]);

          // Iterate through each sub-category
          for (const subCategory of subCategories) {
            const subCategoryModels =
              motorcycleData[category][subCategory] || [];
            if (subCategoryModels.length === 0) continue;

            // Add category heading
            if (isMobile) {
              containerHTML += `
                            <div class="mt-4 mb-2 pl-3">
                                <div class="text-sm font-semibold text-accent border-b border-gray-200 pb-1">
                                    ${formatSubCategoryName(subCategory)}
                                </div>
                            </div>
                        `;
            } else {
              containerHTML += `
                            <div class="category-heading flex items-center gap-2 p-2">
                                <div class="w-1 h-6 bg-accent rounded-r-sm"></div>
                                    <span class="font-semibold text-black text-base">${formatSubCategoryName(
                                      subCategory
                                    )}</span>
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        class="w-4 h-4 text-black ml-auto"
                                        fill="none"
                                        viewBox="0 0 24 24"
                                        stroke="currentColor"
                                        stroke-width="2"
                                    >
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 5l7 7-7 7" />
                                    </svg>
                            </div>
                        `;
            }

            // Add models for this sub-category
            subCategoryModels.forEach((model) => {
              if (isMobile) {
                containerHTML += `
                                <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                                    <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                                    <div class="flex-1">
                                        <div class="text-sm font-medium text-gray-800">${model.name}</div>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </div>
                            `;
              } else {
                containerHTML += `
                                <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                                    <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                                    <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                                </div>
                            `;
              }
            });
          }

          container.innerHTML = containerHTML;
        } else {
          // For specific tabs, just show models without headings
          models = motorcycleData[category][tab] || [];

          if (isMobile) {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="mobile-model-item flex items-center space-x-3 p-3 hover:bg-gray-50 rounded-lg cursor-pointer">
                            <img src="${model.image}" alt="${model.name}" class="w-16 h-10 object-cover rounded">
                            <div class="flex-1">
                                <div class="text-sm font-medium text-gray-800">${model.name}</div>
                            </div>
                            <i class="fas fa-chevron-right text-gray-400"></i>
                        </div>
                    `
              )
              .join("");
          } else {
            container.innerHTML = models
              .map(
                (model) => `
                        <div class="model-item text-center p-3 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <img src="${model.image}" alt="${model.name}" class="mx-auto mb-2 w-24 h-16 object-cover rounded">
                            <p class="text-xs font-medium text-gray-800 mb-1">${model.name}</p>
                        </div>
                    `
              )
              .join("");
          }

          // Show message if no models found
          if (models.length === 0) {
            container.innerHTML = `
                        <div class="${
                          isMobile
                            ? "text-center py-8 text-gray-500"
                            : "col-span-full text-center py-8 text-gray-500"
                        }">
                            No models available in this category
                        </div>
                    `;
          }
        }
      }

      // Initialize with default category and tab
      renderTabs(currentCategory);
      renderModels(currentCategory, currentTab);

      // Add tab event listeners
      function addTabEventListeners(isMobile = false) {
        const selector = isMobile
          ? "#mobile-tabs-container .tab-btn"
          : "#tabs-container .tab-btn";
        const tabs = document.querySelectorAll(selector);

        tabs.forEach((tab) => {
          tab.addEventListener("click", () => {
            const tabName = tab.dataset.tab;
            currentTab = tabName;

            // Update active tab styling
            tabs.forEach((t) => {
              t.classList.remove(
                "text-accent",
                "border-b-2",
                "border-accent",
                "active"
              );
              t.classList.add("text-gray-500");
            });
            tab.classList.remove("text-gray-500");
            tab.classList.add(
              "text-accent",
              "border-b-2",
              "border-accent",
              "active"
            );

            renderModels(currentCategory, tabName, isMobile);
          });
        });
      }

      // Mobile menu functionality
      // Open mobile menu
      mobileMenuBtn.addEventListener("click", () => {
        mobileMenu.classList.remove("hidden");
        mobileMenu.classList.remove("-translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile menu
      function closeMobileMenuFunc() {
        mobileMenu.classList.add("-translate-x-full");
        // Add a small delay before hiding to allow the slide animation to complete
        setTimeout(() => {
          mobileMenu.classList.add("hidden");
        }, 300);
        mobileCategoryDetail.classList.add("-translate-x-full");
        mobileBikesSheet.classList.add("translate-x-full");
        mobileOverlay.classList.add("hidden");
        document.body.style.overflow = "auto";
      }

      closeMobileMenu.addEventListener("click", closeMobileMenuFunc);
      mobileOverlay.addEventListener("click", closeMobileMenuFunc);

      // Handle window resize to ensure mobile menu is hidden on desktop
      window.addEventListener("resize", () => {
        if (window.innerWidth >= 1024) {
          // Close mobile menu when switching to desktop view
          closeMobileMenuFunc();
        }
      });

      // Mobile dropdown functionality
      document.querySelectorAll(".mobile-dropdown-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const content = btn.nextElementSibling;
          const icon = btn.querySelector("i");

          if (content.classList.contains("hidden")) {
            content.classList.remove("hidden");
            icon.classList.remove("fa-chevron-right");
            icon.classList.add("fa-chevron-down");
          } else {
            content.classList.add("hidden");
            icon.classList.remove("fa-chevron-down");
            icon.classList.add("fa-chevron-right");
          }
        });
      });

      // Mobile category selection (show panel)
      document.querySelectorAll(".mobile-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update category title
          document.getElementById(
            "category-title"
          ).textContent = `BIKES / ${category.toUpperCase()}`;

          // Render tabs and models for mobile
          renderTabs(category, true);
          renderModels(category, "all", true);

          // Hide overlay when showing model list
          mobileOverlay.classList.add("hidden");

          // Transition to model view
          mobileMenu.classList.add("-translate-x-full");
          setTimeout(() => {
            mobileCategoryDetail.classList.remove("hidden");
            mobileCategoryDetail.classList.remove("-translate-x-full");
          }, 50);
        });
      });

      // Back to categories (hide panel)
      backToCategories.addEventListener("click", () => {
        mobileCategoryDetail.classList.add("-translate-x-full");
        setTimeout(() => {
          mobileCategoryDetail.classList.add("hidden");
          mobileMenu.classList.remove("-translate-x-full");
          mobileOverlay.classList.remove("hidden");
        }, 300); // match transition duration
      });

      // Close category detail (hide panel)
      closeCategoryDetail.addEventListener("click", () => {
        mobileCategoryDetail.classList.add("-translate-x-full");
        setTimeout(() => {
          mobileCategoryDetail.classList.add("hidden");
          mobileMenu.classList.remove("-translate-x-full");
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "auto";
        }, 300);
      });

      // Desktop category switching
      document.querySelectorAll(".category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Remove active class from all buttons
          document.querySelectorAll(".category-btn").forEach((b) => {
            b.classList.remove("active");
          });

          // Add active class to clicked button
          btn.classList.add("active");

          // Render new tabs and models
          renderTabs(category);
          renderModels(category, "all");
        });
      });

      // Add click handlers for model items
      document.addEventListener("click", (e) => {
        if (
          e.target.closest(".model-item") ||
          e.target.closest(".mobile-model-item")
        ) {
          const modelElement = e.target.closest(
            ".model-item, .mobile-model-item"
          );
          const modelName =
            modelElement.querySelector("p, .text-sm").textContent;
          alert(
            `You selected: ${modelName}\n\nThis would typically navigate to the model details page.`
          );
        }
      });

      // Initialize tabs with event listeners
      addTabEventListeners();

      // Mobile BIKES sheet functionality
      // Function to reset bikes sheet to initial state (category list)
      const resetBikesSheet = () => {
        mobileBikesHeader.textContent = "BIKES";
        mobileBikesCategoriesList.classList.remove("hidden");
        backToBikesCategories.classList.add("hidden");
        mobileBikesTabsContainer.classList.add("hidden");
        mobileBikesModelsContent.classList.add("hidden");
        mobileBikesSheet.classList.add("hidden");
      };

      // Open mobile bikes sheet
      mobileBikesBtn.addEventListener("click", () => {
        mobileBikesSheet.classList.remove("hidden", "translate-x-full");
        mobileOverlay.classList.remove("hidden");
        document.body.style.overflow = "hidden";
      });

      // Close mobile bikes sheet
      const closeBikesSheetFunc = () => {
        mobileBikesSheet.classList.add("translate-x-full");
        if (
          document
            .getElementById("mobile-menu")
            .classList.contains("-translate-x-full")
        ) {
          mobileOverlay.classList.add("hidden");
          document.body.style.overflow = "auto";
        }
        setTimeout(resetBikesSheet, 300); // Reset after transition
      };
      closeBikesSheet.addEventListener("click", closeBikesSheetFunc);

      // Mobile bikes category selection from sheet
      document.querySelectorAll(".mobile-bikes-category-btn").forEach((btn) => {
        btn.addEventListener("click", () => {
          const category = btn.dataset.category;
          currentCategory = category;
          currentTab = "all";

          // Update UI for model view
          mobileBikesHeader.textContent = category.toUpperCase();
          mobileBikesCategoriesList.classList.add("hidden");
          backToBikesCategories.classList.remove("hidden");
          mobileBikesTabsContainer.classList.remove("hidden");
          mobileBikesModelsContent.classList.remove("hidden");

          // Render content for selected category
          renderTabs(category, true, "mobile-bikes-tabs-container");
          renderModels(category, "all", true, "mobile-bikes-models-content");
        });
      });

      // Back to bikes categories
      backToBikesCategories.addEventListener("click", resetBikesSheet);

      // Mobile bikes tab switching
      document.addEventListener("click", (e) => {
        if (e.target.closest("#mobile-bikes-tabs-container .tab-btn")) {
          const tab = e.target.closest("#mobile-bikes-tabs-container .tab-btn");
          const tabName = tab.dataset.tab;
          currentTab = tabName;

          // Update active tab styling
          document
            .querySelectorAll("#mobile-bikes-tabs-container .tab-btn")
            .forEach((t) => {
              t.classList.remove("border-b-2", "border-accent", "active");
              t.classList.add("text-gray-500");
            });
          tab.classList.remove("text-gray-500");
          tab.classList.add(
            "text-accent",
            "border-b-2",
            "border-accent",
            "active"
          );

          renderModels(
            currentCategory,
            tabName,
            true,
            "mobile-bikes-models-content"
          );
        }
      });
    </script>

    <!-- Bike Carousel Functionality -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        console.log("Initializing Bike Carousel...");

        // ========================================
        // DATA SECTION - REPLACE WITH LARAVEL DATA
        // ========================================

        /*
                LARAVEL DEVELOPER INSTRUCTIONS:
          
                1. Replace this entire bikeData object with Laravel Blade syntax like:
          
                const bikeData = @json($bikeData);
          
                2. In your Laravel controller, structure the data exactly like this:
          
                $bikeData = [
                    'PULSAR' => [
                        'logo' => asset('assets/brand-logos/pulsar-logo.png'),
                        'category' => 'Sports',
                        'categoryIcon' => asset('assets/icons/sports.png'),
                        'bikes' => [
                            [
                                'id' => 'pulsar-220f-abs',
                                'name' => 'PULSAR 220F ABS',
                                'image' => asset('assets/bikes/pulsar/pulsar_220f_abs.png'),
                                'description' => 'Experience the perfect blend...',
                                'colors' => [
                                    ['name' => 'black', 'color' => '#000000'],
                                    ['name' => 'yellow', 'color' => '#facc15'],
                                    // ... more colors
                                ]
                            ],
                            // ... more bikes
                        ]
                    ],
                    // ... more brands
                ];
          
                3. Pass this data to your Blade view:
                return view('your-view', compact('bikeData'));
                */

        // STATIC DATA STRUCTURE EXAMPLE (for reference):
        /*
                const staticBikeData = {
                  PULSAR: {
                    logo: "./assets/brand-logos/pulsar-logo.png",
                    bikes: [
                      {
                        id: "pulsar-220f-abs",
                        name: "PULSAR 220F ABS",
                        image: "./assets/bikes/pulsar/pulsar_220f_abs.png",
                        category: "Sports",
                        categoryIcon: "./assets/icons/sports.png",
                        description: "Experience the perfect blend of power and style...",
                        colors: [
                          { name: "black", color: "#000000", image: "./assets/bikes/pulsar/pulsar_220f_abs_black.png", displayName: "Phantom Black" },
                          { name: "yellow", color: "#facc15", image: "./assets/bikes/pulsar/pulsar_220f_abs_yellow.png", displayName: "Lightning Yellow" },
                          { name: "green", color: "#16a34a", image: "./assets/bikes/pulsar/pulsar_220f_abs_green.png", displayName: "Racing Green" }
                        ]
                      }
                    ]
                  },
                  DOMINAR: {
                    logo: "./assets/brand-logos/dominar-logo.svg",
                    bikes: [
                      {
                        id: "dominar-400",
                        name: "DOMINAR 400",
                        image: "./assets/bikes/dominar/dominar_400.png",
                        category: "Adventure",
                        categoryIcon: "./assets/icons/adventure.png",
                        description: "The Dominar 400 is a powerful touring motorcycle...",
                        colors: [
                          { name: "black", color: "#000000", image: "./assets/bikes/dominar/dominar_400_black.png", displayName: "Obsidian Black" },
                          { name: "blue", color: "#2563eb", image: "./assets/bikes/dominar/dominar_400_blue.png", displayName: "Royal Blue" }
                        ]
                      }
                    ]
                  }
                };
                */

        const bikeData = {
          PULSAR: {
            logo: "./assets/brand-logos/pulsar-logo.png",
            bikes: [
              {
                id: "pulsar-ns200",
                name: "PULSAR NS200",
                image:
                  "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/ns200v-teaser-images/new-croppedteaser-webp/ns-webp/banner_w_1.webp",
                category: "NS",
                categoryIcon:
                  "./assets/category-icons/pulsar-category-icon.png",
                description:
                  "The Pulsar NS200 is a sporty naked street bike that offers an exciting riding experience with its powerful engine and agile handling.",
                colors: [
                  {
                    name: "red",
                    color: "#90020B",
                    image:
                      "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/newns200-360-degree/red/ns200-360-webp/00.png",
                    displayName: "Cocktail Wine Red White",
                  },
                  {
                    name: "black  ",
                    color: "#111116",
                    image:
                      "https://cdn.bajajauto.com/-/media/assets/bajajauto/360degreeimages/bikes/pulsar/pulsar-ns-200/new-webp/ns-200-black-webp/00.png",
                    displayName: "Glossy Ebony Black",
                  },
                  {
                    name: "white",
                    color: "#ffffff",
                    image:
                      "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsarns200/newns200-360-degree/ns-200-white-webp/ns-200-white-webp/00.png",
                    displayName: "Metallic Pearl White",
                  },
                  {
                    name: "blue",
                    color: "#233d88",
                    image:
                      "https://cdn.bajajauto.com/-/media/assets/bajajauto/360degreeimages/bikes/pulsar/pulsar-ns-200/new-webp/ns-200-grey-webp/00.png",
                    displayName: "Pewter Blue - Grey",
                  },
                ],
              },
              {
                id: "pulsar-220f-abs",
                name: "PULSAR 220F ABS",
                image:
                  "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/gallery/image6.webp",
                category: "Classic",
                categoryIcon:
                  "./assets/category-icons/pulsar-category-icon.png",
                description:
                  "Experience the perfect blend of power and style with the Pulsar 220F ABS. This high-performance motorcycle delivers an exhilarating ride with advanced features and superior handling.",
                colors: [
                  {
                    name: "red",
                    color: "#90020B",
                    image:
                      "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/ebony-black/00.png",
                    displayName: "Cocktail Wine Red",
                  },
                  {
                    name: "purple",
                    color: "#6450D9",
                    image:
                      "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/purple-fury/00.png",
                    displayName: "Purple Fury",
                  },
                  {
                    name: "green",
                    color: "#16a34a",
                    image:
                      "https://cdn.bajajauto.com/-/media/assets/bajajauto/bikes/pulsar-k-2024/360-images/pewter-grey-new/00.png",
                    displayName: "Racing Green",
                  },
                ],
              },

              {
                id: "pulsar-150-td",
                name: "PULSAR 150 TD",
                image: "./assets/bikes/pulsar/pulsar_150_td.png",
                category: "Classic",
                categoryIcon: "./assets/icons/classic.png",
                description:
                  "The Pulsar 150 TD combines style with efficiency, offering a perfect balance of performance and fuel economy for everyday riding.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/pulsar/pulsar_150_td_black.png",
                    displayName: "Stealth Black",
                  },
                  {
                    name: "red",
                    color: "#dc2626",
                    image: "./assets/bikes/pulsar/pulsar_150_td_red.png",
                    displayName: "Passion Red",
                  },
                  {
                    name: "blue",
                    color: "#2563eb",
                    image: "./assets/bikes/pulsar/pulsar_150_td_blue.png",
                    displayName: "Thunder Blue",
                  },
                ],
              },
              {
                id: "pulsar-rs200",
                name: "PULSAR RS200",
                image: "./assets/bikes/pulsar/pulsar_rs200.png",
                category: "Sports",
                categoryIcon: "./assets/icons/sports.png",
                description:
                  "The Pulsar RS200 is a fully-faired sports bike that combines aggressive styling with high performance for an exhilarating ride.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/pulsar/pulsar_rs200_black.png",
                    displayName: "Shadow Black",
                  },
                  {
                    name: "yellow",
                    color: "#facc15",
                    image: "./assets/bikes/pulsar/pulsar_rs200_yellow.png",
                    displayName: "Solar Yellow",
                  },
                  {
                    name: "red",
                    color: "#dc2626",
                    image: "./assets/bikes/pulsar/pulsar_rs200_red.png",
                    displayName: "Volcanic Red",
                  },
                ],
              },
            ],
          },
          DOMINAR: {
            logo: "./assets/brand-logos/dominar-logo.svg",
            bikes: [
              {
                id: "dominar-400",
                name: "DOMINAR 400",
                image: "./assets/bikes/dominar/dominar_400.png",
                category: "Adventure",
                categoryIcon: "./assets/icons/adventure.png",
                description:
                  "The Dominar 400 is a powerful touring motorcycle designed for long-distance comfort and performance.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/dominar/dominar_400_black.png",
                    displayName: "Obsidian Black",
                  },
                  {
                    name: "blue",
                    color: "#2563eb",
                    image: "./assets/bikes/dominar/dominar_400_blue.png",
                    displayName: "Royal Blue",
                  },
                  {
                    name: "silver",
                    color: "#6b7280",
                    image: "./assets/bikes/dominar/dominar_400_silver.png",
                    displayName: "Chrome Silver",
                  },
                ],
              },
              {
                id: "dominar-250",
                name: "DOMINAR 250",
                image: "./assets/bikes/dominar/dominar_250.png",
                category: "Touring",
                categoryIcon: "./assets/icons/touring.png",
                description:
                  "The Dominar 250 offers a perfect balance of power and efficiency for urban commuting and weekend getaways.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/dominar/dominar_250_black.png",
                    displayName: "Jet Black",
                  },
                  {
                    name: "white",
                    color: "#ffffff",
                    image: "./assets/bikes/dominar/dominar_250_white.png",
                    displayName: "Pearl White",
                  },
                  {
                    name: "red",
                    color: "#dc2626",
                    image: "./assets/bikes/dominar/dominar_250_red.png",
                    displayName: "Scarlet Red",
                  },
                ],
              },
            ],
          },
          AVENGERS: {
            logo: "./assets/brand-logos/avengers-logo.svg",
            bikes: [
              {
                id: "avenger-220",
                name: "AVENGER 220",
                image: "./assets/bikes/avengers/avenger_220.png",
                category: "Cruiser",
                categoryIcon: "./assets/icons/cruiser.png",
                description:
                  "The Avenger 220 combines cruiser comfort with sporty performance for an unmatched riding experience.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/avengers/avenger_220_black.png",
                    displayName: "Midnight Black",
                  },
                  {
                    name: "silver",
                    color: "#6b7280",
                    image: "./assets/bikes/avengers/avenger_220_silver.png",
                    displayName: "Metallic Silver",
                  },
                  {
                    name: "maroon",
                    color: "#7f1d1d",
                    image: "./assets/bikes/avengers/avenger_220_maroon.png",
                    displayName: "Burgundy Red",
                  },
                ],
              },
              {
                id: "avenger-160",
                name: "AVENGER 160",
                image: "./assets/bikes/avengers/avenger_160.png",
                category: "Cruiser",
                categoryIcon: "./assets/icons/cruiser.png",
                description:
                  "The Avenger 160 offers the perfect blend of style and comfort for urban cruising.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/avengers/avenger_160_black.png",
                    displayName: "Phantom Black",
                  },
                  {
                    name: "blue",
                    color: "#2563eb",
                    image: "./assets/bikes/avengers/avenger_160_blue.png",
                    displayName: "Ocean Blue",
                  },
                  {
                    name: "white",
                    color: "#ffffff",
                    image: "./assets/bikes/avengers/avenger_160_white.png",
                    displayName: "Crystal White",
                  },
                ],
              },
            ],
          },
          DISCOVER: {
            logo: "./assets/brand-logos/discover-logo.svg",
            bikes: [
              {
                id: "discover-125",
                name: "DISCOVER 125",
                image: "./assets/bikes/discover/discover_125.png",
                category: "Commuter",
                categoryIcon: "./assets/icons/commuter.png",
                description:
                  "The Discover 125 is designed for efficient urban commuting with its fuel-efficient engine and comfortable riding position.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/discover/discover_125_black.png",
                    displayName: "Stealth Black",
                  },
                  {
                    name: "red",
                    color: "#dc2626",
                    image: "./assets/bikes/discover/discover_125_red.png",
                    displayName: "Racing Red",
                  },
                  {
                    name: "blue",
                    color: "#2563eb",
                    image: "./assets/bikes/discover/discover_125_blue.png",
                    displayName: "Electric Blue",
                  },
                ],
              },
              {
                id: "discover-110",
                name: "DISCOVER 110",
                image: "./assets/bikes/discover/discover_110.png",
                category: "Commuter",
                categoryIcon: "./assets/icons/commuter.png",
                description:
                  "The Discover 110 offers excellent fuel efficiency and low maintenance costs for daily commuting.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/discover/discover_110_black.png",
                    displayName: "Midnight Black",
                  },
                  {
                    name: "silver",
                    color: "#6b7280",
                    image: "./assets/bikes/discover/discover_110_silver.png",
                    displayName: "Metallic Silver",
                  },
                  {
                    name: "green",
                    color: "#16a34a",
                    image: "./assets/bikes/discover/discover_110_green.png",
                    displayName: "Forest Green",
                  },
                ],
              },
            ],
          },
          PLATINA: {
            logo: "./assets/brand-logos/platina-logo.svg",
            bikes: [
              {
                id: "platina-110",
                name: "PLATINA 110",
                image: "./assets/bikes/platina/platina_110.png",
                category: "Economy",
                categoryIcon: "./assets/icons/economy.png",
                description:
                  "The Platina 110 is Bajaj's most fuel-efficient motorcycle, perfect for budget-conscious riders.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/platina/platina_110_black.png",
                    displayName: "Ebony Black",
                  },
                  {
                    name: "red",
                    color: "#dc2626",
                    image: "./assets/bikes/platina/platina_110_red.png",
                    displayName: "Racing Red",
                  },
                  {
                    name: "silver",
                    color: "#6b7280",
                    image: "./assets/bikes/platina/platina_110_silver.png",
                    displayName: "Platinum Silver",
                  },
                ],
              },
              {
                id: "platina-100",
                name: "PLATINA 100",
                image: "./assets/bikes/platina/platina_100.png",
                category: "Economy",
                categoryIcon: "./assets/icons/economy.png",
                description:
                  "The Platina 100 offers unbeatable fuel efficiency and low maintenance costs for daily commuting.",
                colors: [
                  {
                    name: "black",
                    color: "#000000",
                    image: "./assets/bikes/platina/platina_100_black.png",
                    displayName: "Carbon Black",
                  },
                  {
                    name: "blue",
                    color: "#2563eb",
                    image: "./assets/bikes/platina/platina_100_blue.png",
                    displayName: "Ocean Blue",
                  },
                  {
                    name: "white",
                    color: "#ffffff",
                    image: "./assets/bikes/platina/platina_100_white.png",
                    displayName: "Pearl White",
                  },
                ],
              },
            ],
          },
        };

        // Bike carousel state
        let currentBrand = "PULSAR";
        let currentBikeIndex = 0;
        let currentColorIndex = 0;

        // DOM elements
        const backgroundText = document.getElementById("background-brand-text");
        const bikeTitle = document.getElementById("bike-title");
        const bikeDescription = document.getElementById("bike-description");
        const mainBikeImage = document.getElementById("main-bike-image");
        const brandLogo = document.getElementById("brand-logo");
        const categoryIcon = document.getElementById("category-icon");
        const categoryText = document.getElementById("category-text");
        const colorSelection = document.getElementById("color-selection");
        const brandTabsContainer = document.getElementById(
          "brand-tabs-container"
        );
        const modelTabsContainer = document.getElementById(
          "model-tabs-container"
        );
        const prevBtn = document.getElementById("bike-prev-btn");
        const nextBtn = document.getElementById("bike-next-btn");

        console.log("DOM elements found:", {
          backgroundText: !!backgroundText,
          bikeTitle: !!bikeTitle,
          mainBikeImage: !!mainBikeImage,
          brandTabsContainer: !!brandTabsContainer,
          modelTabsContainer: !!modelTabsContainer,
        });

        // Function to generate brand tabs dynamically
        function generateBrandTabs() {
          if (!brandTabsContainer) return;

          brandTabsContainer.innerHTML = "";
          const brands = Object.keys(bikeData);

          brands.forEach((brand, index) => {
            const brandTab = document.createElement("button");
            brandTab.className = `brand-tab text-lg lg:text-xl font-semibold border-b-2 pb-2 ${
              index === 0
                ? "active text-gray-800 border-black"
                : "text-gray-400 border-transparent hover:text-gray-600"
            }`;
            brandTab.dataset.brand = brand;
            brandTab.textContent = brand;

            brandTab.addEventListener("click", () => {
              switchBrand(brand);
            });

            brandTabsContainer.appendChild(brandTab);
          });
        }

        // Function to generate model tabs dynamically
        function generateModelTabs() {
          if (!modelTabsContainer) return;

          modelTabsContainer.innerHTML = "";
          const brandData = bikeData[currentBrand];
          const modelGroup = document.createElement("div");
          modelGroup.className = `model-group ${currentBrand.toLowerCase()}-models`;
          const modelButtonsContainer = document.createElement("div");

          // Make model tabs horizontally scrollable like brand tabs
          modelButtonsContainer.className =
            "inline-flex gap-2 border-t border-gray-200 px-4 overflow-x-auto whitespace-nowrap";

          brandData.bikes.forEach((bike, index) => {
            const modelBtn = document.createElement("button");
            modelBtn.className = `variant-btn px-4 py-2 text-base font-bold border-t-2 flex-shrink-0 ${
              index === currentBikeIndex
                ? "active text-gray-700 border-black"
                : "text-gray-500 border-transparent"
            }`;
            modelBtn.dataset.model = bike.id;
            modelBtn.dataset.image = bike.image;
            modelBtn.dataset.name = bike.name;
            modelBtn.dataset.description = bike.description;
            modelBtn.textContent = bike.name;
            modelBtn.addEventListener("click", () => {
              currentBikeIndex = index;
              currentColorIndex = 0;
              updateDisplay();

              // Scroll the active button into view for better UX
              setTimeout(() => {
                modelBtn.scrollIntoView({
                  behavior: 'smooth',
                  block: 'nearest',
                  inline: 'center'
                });
              }, 100);
            });
            modelButtonsContainer.appendChild(modelBtn);
          });
          modelGroup.appendChild(modelButtonsContainer);
          modelTabsContainer.innerHTML = "";
          modelTabsContainer.appendChild(modelGroup);
        }

        // Helper function to get color style
        function getColorStyle(colorName) {
          const colorMap = {
            black: "#000000",
            white: "#ffffff",
            red: "#dc2626",
            blue: "#2563eb",
            yellow: "#facc15",
            green: "#16a34a",
            orange: "#ea580c",
            silver: "#6b7280",
            maroon: "#7f1d1d",
          };
          return colorMap[colorName] || "#000000";
        }

        // Update display function
        function updateDisplay() {
          const brandData = bikeData[currentBrand];
          const currentBike = brandData.bikes[currentBikeIndex];
          const currentColor = currentBike.colors[currentColorIndex];

          console.log("Updating display:", {
            brand: currentBrand,
            bikeIndex: currentBikeIndex,
            colorIndex: currentColorIndex,
            bike: currentBike.name,
          });

          // Update background text
          if (backgroundText) {
            backgroundText.textContent = currentBrand;

            // Toggle pulsar-text class based on brand
            if (currentBrand === "PULSAR") {
              backgroundText.classList.add("pulsar-text");
              backgroundText.classList.remove("other-brand-text");
            } else {
              backgroundText.classList.remove("pulsar-text");
              backgroundText.classList.add("other-brand-text");
            }
          }

          // Update bike title and description
          if (bikeTitle) {
            bikeTitle.textContent = currentBike.name;
          }
          if (bikeDescription) {
            bikeDescription.textContent = currentBike.description;
          }

          // Update main bike image
          if (mainBikeImage) {
            // Use the current selected color's image, fallback to first color, then default bike image
            const currentColor =
              currentBike.colors && currentBike.colors.length > 0
                ? currentBike.colors[currentColorIndex]
                : null;
            const firstColor =
              currentBike.colors && currentBike.colors.length > 0
                ? currentBike.colors[0]
                : null;
            const imageToUse =
              currentColor && currentColor.image
                ? currentColor.image
                : firstColor && firstColor.image
                ? firstColor.image
                : currentBike.image;
            mainBikeImage.src = imageToUse;
            mainBikeImage.alt = currentBike.name;
          }

          // Update brand logo and category
          if (brandLogo) {
            brandLogo.src = brandData.logo;
            brandLogo.alt = currentBrand + " Logo";
          }
          if (categoryIcon) {
            categoryIcon.src = currentBike.categoryIcon;
            categoryIcon.alt = currentBike.category + " Category";
          }
          if (categoryText) {
            categoryText.textContent = currentBike.category;
          }

          // Update color selection with custom names and images
          if (colorSelection) {
            // Clear existing content
            colorSelection.innerHTML = "";

            // Create color name display
            const colorNameDisplay = document.createElement("div");
            colorNameDisplay.className = "text-center";
            const currentColor =
              currentBike.colors && currentBike.colors.length > 0
                ? currentBike.colors[currentColorIndex]
                : null;
            colorNameDisplay.innerHTML = `
            <div class="font-medium text-gray-900" id="current-color-name">${
              currentColor ? currentColor.displayName : "Black"
            }</div>
          `;
            colorSelection.appendChild(colorNameDisplay);

            // Create color buttons container
            const colorButtonsContainer = document.createElement("div");
            colorButtonsContainer.className = "flex space-x-2";

            currentBike.colors.forEach((color, index) => {
              const colorBtn = document.createElement("button");
              colorBtn.className = `color-btn w-8 h-8 rounded-full border-2 border-[#326AD2] ${
                index === currentColorIndex ? "active" : ""
              }`;
              colorBtn.style.backgroundColor = color.color;
              colorBtn.dataset.color = color.name;
              colorBtn.dataset.colorName = color.displayName;
              colorBtn.dataset.image = color.image;
              colorBtn.title = color.displayName; // Show color name on hover

              colorBtn.addEventListener("click", () => {
                currentColorIndex = index;
                updateDisplay();
              });

              colorButtonsContainer.appendChild(colorBtn);
            });

            colorSelection.appendChild(colorButtonsContainer);
          }

          // Update brand tabs
          brandTabsContainer.querySelectorAll(".brand-tab").forEach((tab) => {
            if (tab.dataset.brand === currentBrand) {
              tab.classList.add("active");
              tab.classList.remove("text-gray-400");
              tab.classList.add("text-gray-800", "border-[#326AD2]");
              tab.classList.remove("border-transparent");
            } else {
              tab.classList.remove("active");
              tab.classList.add("text-gray-400");
              tab.classList.remove("text-gray-800", "border-[#326AD2]");
              tab.classList.add("border-transparent");
            }
          });

          // Update model tabs
          modelTabsContainer
            .querySelectorAll(".model-group")
            .forEach((group) => {
              if (
                group.classList.contains(currentBrand.toLowerCase() + "-models")
              ) {
                group.classList.remove("hidden");
                const buttons = group.querySelectorAll(".variant-btn");
                buttons.forEach((btn, index) => {
                  if (index === currentBikeIndex) {
                    btn.classList.add("active");
                    btn.classList.remove("text-gray-500", "border-transparent");
                    btn.classList.add("text-gray-700", "border-black");
                  } else {
                    btn.classList.remove("active");
                    btn.classList.add("text-gray-500", "border-transparent");
                    btn.classList.remove("text-gray-700", "border-black");
                  }
                });
              } else {
                group.classList.add("hidden");
              }
            });
        }

        // Navigation functions
        function nextBike() {
          const brandData = bikeData[currentBrand];
          currentBikeIndex = (currentBikeIndex + 1) % brandData.bikes.length;
          currentColorIndex = 0; // Reset color when changing bike
          updateDisplay();
        }

        function prevBike() {
          const brandData = bikeData[currentBrand];
          currentBikeIndex =
            (currentBikeIndex - 1 + brandData.bikes.length) %
            brandData.bikes.length;
          currentColorIndex = 0; // Reset color when changing bike
          updateDisplay();
        }

        function switchBrand(brand) {
          if (bikeData[brand]) {
            currentBrand = brand;
            currentBikeIndex = 0;
            currentColorIndex = 0;
            generateModelTabs(); // Regenerate model tabs for the new brand
            updateDisplay();
          }
        }

        function capitalizeFirstLetter(str) {
          if (!str) return "";
          return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
        }

        // ========================================
        // LARAVEL INTEGRATION SETUP
        // ========================================

        // Make bike data available globally for the carousel
        window.bikeData = bikeData;

        console.log("✅ Bike data loaded and available globally");
        console.log("📊 Brands available:", Object.keys(bikeData));

        // Generate tabs dynamically
        generateBrandTabs();
        generateModelTabs();

        // Add event listeners for navigation buttons
        if (prevBtn) {
          prevBtn.addEventListener("click", prevBike);
        }
        if (nextBtn) {
          nextBtn.addEventListener("click", nextBike);
        }

        // Initialize the display
        updateDisplay();
      });
    </script>

    <!-- Expereince Carousel Functionality -->
    <script>
      const slides = document.querySelectorAll(".expreience-carousel-slide");
      const experiencePrevBtn = document.getElementById("prevBtn");
      const experienceNextBtn = document.getElementById("nextBtn");

      let current = 1; // center index

      // Define transform classes for different screen sizes
      const transformClasses = {
        // Extra-Small screens (for very narrow phones, e.g., < 480px)
        // Adjust these translate-x values to prevent overflow.
        // Also, consider a smaller scale for side elements on tiny screens.
        xs: [
          "z-5 opacity-70 transform -translate-x-16 -translate-z-12 rotate-y-12 scale-80", // left: reduced translate-x, slightly smaller scale
          "z-10 transform translate-x-0 translate-z-0 scale-100", // center
          "z-5 opacity-70 transform translate-x-16 -translate-z-12 -rotate-y-12 scale-80", // right: reduced translate-x, slightly smaller scale
        ],
        // Small to Medium screens (approx. 480px to 1023px)
        sm: [
          "z-5 opacity-80 transform -translate-x-48 -translate-z-24 rotate-y-12 scale-90", // left
          "z-10 transform translate-x-0 translate-z-0 scale-100", // center
          "z-5 opacity-80 transform translate-x-48 -translate-z-24 -rotate-y-12 scale-90", // right
        ],
        // For large screens (lg breakpoint and up, >= 1024px)
        lg: [
          "z-5 opacity-80 transform -translate-x-64 -translate-z-32 rotate-y-12 scale-90", // left
          "z-10 transform translate-x-0 translate-z-0 scale-100", // center
          "z-5 opacity-80 transform translate-x-64 -translate-z-32 -rotate-y-12 scale-90", // right
        ],
      };

      // Function to get the current breakpoint's transform classes
      function getCurrentTransformClasses() {
        const screenWidth = window.innerWidth;
        // Default Tailwind breakpoints: sm: 640px, md: 768px, lg: 1024px
        // Let's refine the ranges for transform application
        if (screenWidth >= 1024) {
          return transformClasses.lg;
        } else if (screenWidth >= 480) {
          // Using 480px as a breakpoint for 'sm' values
          return transformClasses.sm;
        } else {
          // For screens smaller than 480px
          return transformClasses.xs;
        }
      }

      function updateSlides() {
        const currentTransformClasses = getCurrentTransformClasses();
        const screenWidth = window.innerWidth;

        slides.forEach((slide, i) => {
          // Start with only the classes that are always present and not related to position/transform
          let baseClasses =
            "expreience-carousel-slide absolute rounded-3xl overflow-hidden shadow-2xl";

          // Dynamically apply size based on screen width
          if (screenWidth >= 1024) {
            baseClasses += " lg:w-[480px] lg:h-[630px]";
          } else {
            baseClasses += " w-80 h-96"; // Default size for smaller screens
          }

          slide.className = baseClasses; // Reset and apply base classes

          const btnWrapper = slide.querySelector(".explore-btn-wrapper");
          btnWrapper.classList.add("hidden");

          const relativeIndex = (i - current + slides.length) % slides.length;

          // Apply the specific transform for the current relative index
          if (relativeIndex === 0) {
            slide.className += " " + currentTransformClasses[0]; // Left slide
          } else if (relativeIndex === 1) {
            slide.className += " " + currentTransformClasses[1]; // Center slide
            btnWrapper.classList.remove("hidden"); // show explore button on center
          } else if (relativeIndex === 2) {
            slide.className += " " + currentTransformClasses[2]; // Right slide
          }
          // Hide slides that are not one of the three active (optional, but good for performance/visuals)
          else {
            slide.style.display = "none"; // Temporarily hide
          }
        });

        // After updating all, ensure active slides are visible
        slides.forEach((slide, i) => {
          const relativeIndex = (i - current + slides.length) % slides.length;
          if (
            relativeIndex === 0 ||
            relativeIndex === 1 ||
            relativeIndex === 2
          ) {
            slide.style.display = ""; // Show
          }
        });
      }

      experiencePrevBtn.addEventListener("click", () => {
        current = (current - 1 + slides.length) % slides.length;
        updateSlides();
      });

      experienceNextBtn.addEventListener("click", () => {
        current = (current + 1) % slides.length;
        updateSlides();
      });

      // Re-run updateSlides on window resize to apply correct transforms and sizes
      window.addEventListener("resize", updateSlides);

      updateSlides(); // initial render
    </script>

    <!-- Scroll to Bikes Functionality -->
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const scrollIndicator = document.getElementById("scroll-to-bikes");
        const bikeCarouselSection = document.getElementById("bike-carousel");

        if (scrollIndicator && bikeCarouselSection) {
          scrollIndicator.addEventListener("click", function () {
            // Smooth scroll to the bike carousel section
            bikeCarouselSection.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          });
        }
      });
    </script>
  </body>
</html>
